<?php

use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\EmployeePhotoController;
use App\Http\Controllers\BarcodeController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;


Route::get('/', [AttendanceController::class, 'index'])->name('attendance.index');
Route::post('/scan', [AttendanceController::class, 'store'])->name('attendance.store');
Route::get('/employee-lookup/{bioId}', [AttendanceController::class, 'lookupEmployee'])->name('employee.lookup');



Route::resource('employees', EmployeeController::class);
Route::get('/employee-photo/{employee}', [EmployeePhotoController::class, 'show'])->name('employee.photo');


Route::get('/barcode/{employee}', [BarcodeController::class, 'show'])->name('barcode.show');
Route::get('/barcode/{employee}/print', [BarcodeController::class, 'print'])->name('barcode.print');
Route::get('/barcode/{employee}/download', [BarcodeController::class, 'download'])->name('barcode.download');


Route::get('/attendance-logs', [AttendanceController::class, 'logs'])->name('attendance.logs');
Route::get('/attendance-logs/export', [AttendanceController::class, 'exportExcel'])->name('attendance.logs.export');
Route::get('/attendance-logs/{employee}', [AttendanceController::class, 'employeeLogs'])->name('attendance.employee-logs');





require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
