<?php

namespace App\Http\Controllers;

use App\Events\AttendanceRecorded;
use App\Http\Requests\AttendanceLogsRequest;
use App\Http\Requests\RecordAttendanceRequest;
use App\Models\Employee;
use App\Repositories\AttendanceRepository;
use App\Services\AttendanceService;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;

/**
 * Attendance Controller
 *
 * Thin controller that handles HTTP requests/responses for attendance operations.
 * Business logic is delegated to AttendanceService and data access to AttendanceRepository.
 *
 * @package App\Http\Controllers
 */
class AttendanceController extends Controller
{
    public function __construct(
        private AttendanceService $attendanceService,
        private AttendanceRepository $attendanceRepository
    ) {}

    /**
     * Display the attendance scanning interface
     */
    public function index(): InertiaResponse
    {
        return Inertia::render('Scan');
    }

    /**
     * Lookup employee by BioID
     */
    public function lookupEmployee(string $bioId): JsonResponse
    {
        $result = $this->attendanceService->lookupEmployee($bioId);
        $statusCode = $result['success'] ? 200 : 404;
        return response()->json($result, $statusCode);
    }

    /**
     * Record attendance action
     */
    public function store(RecordAttendanceRequest $request)
    {
        $result = $this->attendanceService->recordAttendance(
            $request->validated()['employee_code'],
            $request->validated()['Emp_Type']
        );

        // Fire event if successful
        if ($result['success'] && isset($result['data'])) {
            event(new AttendanceRecorded(
                $result['data']['log'],
                $result['data']['employee'],
                $result['data']['action_label'],
                $request->getValidatedData()
            ));
        }

        $flashType = $result['severity'] === 'success' ? 'success' : 'error';

        return redirect()->back()->with($flashType, $result['message']);
    }


    /**
     * Display attendance logs with filtering and pagination
     */
    public function logs(AttendanceLogsRequest $request): InertiaResponse
    {
        $filters = $request->getFilters();
        $paginationOptions = $request->getPaginationOptions();

        // Get attendance logs using repository
        $logs = $this->attendanceRepository->getAttendanceLogs(
            $filters,
            $paginationOptions['per_page']
        );

        // Get active employees for filtering
        $employees = $this->attendanceRepository->getActiveEmployees();

        // Action types for filtering
        $actionTypes = [
            ['action_type' => 1, 'action_label' => 'Check In'],
            ['action_type' => 2, 'action_label' => 'Check Out'],
        ];

        // Generate merged sessions and hours summary using service
        $mergedSessions = $this->attendanceService->generateMergedSessions($filters);
        $hoursWorkedSummary = $this->attendanceService->generateHoursWorkedSummary($filters);

        return Inertia::render('Attendance/Logs', [
            'logs' => $logs,
            'mergedSessions' => $mergedSessions,
            'employees' => $employees,
            'actionTypes' => $actionTypes,
            'hoursWorkedSummary' => $hoursWorkedSummary,
            'filters' => $request->getFilters(),
        ]);
    }

    /**
     * Export attendance data as CSV
     */
    public function exportExcel(AttendanceLogsRequest $request)
    {
        $filters = $request->getFilters();

        // Generate consolidated hours data
        $sessions = $this->attendanceService->generateMergedSessions($filters);
        $consolidatedData = $this->attendanceService->generateConsolidatedHours($sessions);

        $filename = 'consolidated_hours_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($consolidatedData, $request) {
            $file = fopen('php://output', 'w');

            // Add header with date range info
            $dateRange = $this->getDateRangeString($request->getFilters());
            fputcsv($file, [$dateRange]);
            fputcsv($file, []); // Empty row

            // CSV Headers for Consolidated Hours
            fputcsv($file, [
                'Employee Name',
                'Employee ID',
                'Total Hours Worked',
                'Total Sessions',
                'Working Days',
                'Average Hours per Day'
            ]);

            // CSV Data for Consolidated Hours
            foreach ($consolidatedData as $data) {
                fputcsv($file, [
                    $data['employee']->EMP_FullName ?? 'Unknown',
                    $data['employee']->EMP_EmpNo,
                    round($data['total_hours'], 2),
                    $data['session_count'],
                    $data['working_days'],
                    round($data['average_hours_per_day'], 2)
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get date range string for report header
     */
    private function getDateRangeString(array $filters): string
    {
        if (isset($filters['date_from']) && isset($filters['date_to'])) {
            return "Date Range: {$filters['date_from']} to {$filters['date_to']}";
        } elseif (isset($filters['date_from'])) {
            return "From: {$filters['date_from']}";
        } elseif (isset($filters['date_to'])) {
            return "Until: {$filters['date_to']}";
        } else {
            return "Date Range: Last 7 days";
        }
    }

    /**
     * Display attendance logs for a specific employee
     */
    public function employeeLogs(AttendanceLogsRequest $request, Employee $employee): InertiaResponse
    {
        $filters = $request->getFilters();
        $paginationOptions = $request->getPaginationOptions();

        // Get employee attendance logs using repository
        $logs = $this->attendanceRepository->getEmployeeAttendanceLogs(
            $employee,
            $filters,
            $paginationOptions['per_page']
        );

        return Inertia::render('Attendance/EmployeeLogs', [
            'employee' => $employee,
            'logs' => $logs,
            'filters' => $request->getFilters(),
        ]);
    }
}

