<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

/**
 * Employee Model
 *
 * Represents an employee in the Time Attendance System with comprehensive
 * functionality for attendance tracking, photo management, and business logic.
 *
 * @property string $EMP_EmpNo Employee number (primary key)
 * @property int|null $EMP_EmpID Employee ID
 * @property string $EMP_BioID Unique barcode ID for attendance scanning
 * @property string $EMP_FirstName Employee first name
 * @property string $EMP_LastName Employee last name
 * @property string|null $EMP_MiddleName Employee middle name
 * @property string|null $EMP_FullName Full name (auto-generated)
 * @property string|null $EMP_Department Department name
 * @property int $EMP_IsActive Active status (1=active, 0=inactive)
 * @property string|null $EMP_Type Employee type
 * @property string|null $EMP_PhotoPath Path to employee photo
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 *
 * @property-read string $full_name Computed full name
 * @property-read string|null $photo_url Photo URL with fallback to placeholder
 * @property-read string $status Status as string (active/inactive)
 * @property-read bool $is_checked_in Whether employee is currently checked in
 * @property-read string $formatted_emp_no Formatted employee number
 * @property-read string $display_name Full name with employee number
 * @property-read string $department_display Department with fallback
 *
 * @method static \Illuminate\Database\Eloquent\Builder active() Get only active employees
 * @method static \Illuminate\Database\Eloquent\Builder inactive() Get only inactive employees
 * @method static \Illuminate\Database\Eloquent\Builder search(string $search) Search by name or ID
 * @method static \Illuminate\Database\Eloquent\Builder byDepartment(string $department) Filter by department
 * @method static \Illuminate\Database\Eloquent\Builder byType(string $type) Filter by employee type
 * @method static \Illuminate\Database\Eloquent\Builder withPhotos() Get employees with photos
 * @method static \Illuminate\Database\Eloquent\Builder withoutPhotos() Get employees without photos
 *
 * @package App\Models
 * <AUTHOR> Attendance System
 * @version 1.0.0
 */
class Employee extends Model
{
    use HasFactory;

    protected $table = 'Employee';
    protected $primaryKey = 'EMP_EmpNo';
    public $incrementing = false; // Since EMP_EmpNo is not auto-incrementing
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'EMP_EmpID',
        'EMP_BioID',
        'EMP_FirstName',
        'EMP_LastName',
        'EMP_MiddleName',
        'EMP_FullName',
        'EMP_Department',
        'EMP_IsActive',
        'EMP_Type',
        'EMP_PhotoPath',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        // Add any sensitive fields here if needed
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'EMP_IsActive' => 'integer',
        'EMP_Type' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     */
    protected $appends = [
        'full_name',
        'photo_url',
        'status',
        'is_checked_in',
    ];

    /**
     * Validation rules for the model
     */
    public static function validationRules(): array
    {
        return [
            'EMP_EmpNo' => 'nullable|string|max:50|unique:Employee,EMP_EmpNo',
            'EMP_BioID' => 'nullable|string|max:20|unique:Employee,EMP_BioID',
            'EMP_FirstName' => 'required|string|max:100',
            'EMP_LastName' => 'required|string|max:100',
            'EMP_MiddleName' => 'nullable|string|max:100',
            'EMP_FullName' => 'nullable|string|max:255',
            'EMP_Department' => 'nullable|string|max:100',
            'EMP_IsActive' => 'required|integer|in:0,1',
            'EMP_Type' => 'nullable|string|max:50',
            'EMP_PhotoPath' => 'nullable|string|max:500',
        ];
    }

    /**
     * Validation rules for updating an existing employee
     */
    public static function updateValidationRules(int $employeeId): array
    {
        $rules = self::validationRules();
        $rules['EMP_EmpNo'] = 'nullable|string|max:50|unique:Employee,EMP_EmpNo,' . $employeeId . ',EMP_EmpID';
        $rules['EMP_BioID'] = 'nullable|string|max:20|unique:Employee,EMP_BioID,' . $employeeId . ',EMP_EmpID';
        return $rules;
    }

    // ===================================
    // STATIC METHODS
    // ===================================

    /**
     * Generate unique BioID for barcode
     */
    public static function generateBioID(): string
    {
        do {
            // Generate a 8-digit numeric ID with prefix
            $bioId = 'EMP' . str_pad(mt_rand(10000, 99999), 5, '0', STR_PAD_LEFT);
        } while (self::where('EMP_BioID', $bioId)->exists());

        return $bioId;
    }

    // ===================================
    // RELATIONSHIPS
    // ===================================

    /**
     * Get attendance logs for this employee
     */
    public function attendanceLogs(): HasMany
    {
        return $this->hasMany(AttendanceLog::class, 'EMP_EmpNo', 'EMP_EmpNo');
    }

    /**
     * Get today's attendance logs
     */
    public function todayLogs()
    {
        $clarionToday = AttendanceLog::toClarionDate(today());
        return $this->attendanceLogs()
            ->where('log_date', $clarionToday)
            ->orderBy('logged_at');
    }

    /**
     * Get recent attendance logs (last 10)
     */
    public function recentLogs()
    {
        return $this->attendanceLogs()
            ->latest('logged_at')
            ->limit(10);
    }

    // ===================================
    // QUERY SCOPES
    // ===================================

    /**
     * Scope to get only active employees
     */
    public function scopeActive($query)
    {
        return $query->where('EMP_IsActive', 1);
    }

    /**
     * Scope to get only inactive employees
     */
    public function scopeInactive($query)
    {
        return $query->where('EMP_IsActive', 0);
    }

    /**
     * Scope to search employees by name or ID
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('EMP_FirstName', 'like', "%{$search}%")
              ->orWhere('EMP_LastName', 'like', "%{$search}%")
              ->orWhere('EMP_FullName', 'like', "%{$search}%")
              ->orWhere('EMP_BioID', 'like', "%{$search}%")
              ->orWhere('EMP_EmpNo', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to filter by department
     */
    public function scopeByDepartment($query, string $department)
    {
        return $query->where('EMP_Department', $department);
    }

    /**
     * Scope to filter by employee type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('EMP_Type', $type);
    }

    /**
     * Scope to get employees with photos
     */
    public function scopeWithPhotos($query)
    {
        return $query->whereNotNull('EMP_PhotoPath')
                    ->where('EMP_PhotoPath', '!=', '');
    }

    /**
     * Scope to get employees without photos
     */
    public function scopeWithoutPhotos($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('EMP_PhotoPath')
              ->orWhere('EMP_PhotoPath', '');
        });
    }

    // ===================================
    // BUSINESS LOGIC METHODS
    // ===================================

    /**
     * Get the last attendance action
     */
    public function lastAttendanceAction()
    {
        return $this->attendanceLogs()
            ->latest('logged_at')
            ->first();
    }

    /**
     * Check if employee is currently checked in
     */
    public function isCheckedIn(): bool
    {
        $lastAction = $this->lastAttendanceAction();
        if (!$lastAction) {
            return false;
        }

        // Using numeric action types: 1=check_in, 3=break_in
        return in_array($lastAction->action_type, [1, 3]);
    }

    /**
     * Check if employee is active (can perform attendance actions)
     */
    public function isActive(): bool
    {
        return $this->EMP_IsActive === 1;
    }

    /**
     * Check if employee can perform attendance actions
     */
    public function canPerformAttendanceActions(): bool
    {
        return $this->isActive();
    }

    /**
     * Get current attendance status
     */
    public function getCurrentAttendanceStatus(): string
    {
        if (!$this->isActive()) {
            return 'inactive';
        }

        $lastAction = $this->lastAttendanceAction();
        if (!$lastAction) {
            return 'not_checked_in';
        }

        switch ($lastAction->action_type) {
            case 1: // check_in
                return 'checked_in';
            case 2: // check_out
                return 'checked_out';
            case 3: // break_in
                return 'on_break';
            case 4: // break_out
                return 'back_from_break';
            default:
                return 'unknown';
        }
    }

    /**
     * Get next allowed attendance actions
     */
    public function getNextAllowedActions(): array
    {
        if (!$this->canPerformAttendanceActions()) {
            return [];
        }

        $status = $this->getCurrentAttendanceStatus();

        switch ($status) {
            case 'not_checked_in':
            case 'checked_out':
                return [1]; // check_in only
            case 'checked_in':
            case 'back_from_break':
                return [2, 3]; // check_out or break_in
            case 'on_break':
                return [4]; // break_out only
            default:
                return [1]; // Default to check_in
        }
    }

    /**
     * Calculate total work hours for a specific date
     */
    public function calculateWorkHours(Carbon $date): float
    {
        $clarionDate = AttendanceLog::toClarionDate($date);
        $logs = $this->attendanceLogs()
            ->where('log_date', $clarionDate)
            ->orderBy('logged_at')
            ->get();

        if ($logs->isEmpty()) {
            return 0.0;
        }

        $totalMinutes = 0;
        $checkInTime = null;
        $breakStartTime = null;

        foreach ($logs as $log) {
            switch ($log->action_type) {
                case 1: // check_in
                    $checkInTime = $log->logged_at;
                    break;
                case 2: // check_out
                    if ($checkInTime) {
                        $totalMinutes += $checkInTime->diffInMinutes($log->logged_at);
                        $checkInTime = null;
                    }
                    break;
                case 3: // break_in
                    if ($checkInTime) {
                        $totalMinutes += $checkInTime->diffInMinutes($log->logged_at);
                    }
                    $breakStartTime = $log->logged_at;
                    break;
                case 4: // break_out
                    $checkInTime = $log->logged_at; // Resume work time
                    $breakStartTime = null;
                    break;
            }
        }

        // If still checked in, calculate up to now
        if ($checkInTime && $date->isToday()) {
            $totalMinutes += $checkInTime->diffInMinutes(now());
        }

        return round($totalMinutes / 60, 2); // Convert to hours
    }

    /**
     * Calculate total work hours for current week
     */
    public function calculateWeeklyHours(): float
    {
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();
        $totalHours = 0;

        for ($date = $startOfWeek->copy(); $date->lte($endOfWeek); $date->addDay()) {
            $totalHours += $this->calculateWorkHours($date);
        }

        return round($totalHours, 2);
    }

    /**
     * Calculate total work hours for current month
     */
    public function calculateMonthlyHours(): float
    {
        $startOfMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();
        $totalHours = 0;

        for ($date = $startOfMonth->copy(); $date->lte($endOfMonth); $date->addDay()) {
            $totalHours += $this->calculateWorkHours($date);
        }

        return round($totalHours, 2);
    }

    // ===================================
    // ACCESSOR METHODS
    // ===================================

    /**
     * Get employee status as string for display
     */
    public function getStatusAttribute(): string
    {
        return $this->EMP_IsActive === 1 ? 'active' : 'inactive';
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return $this->EMP_FullName ?: trim($this->EMP_FirstName . ' ' . $this->EMP_MiddleName . ' ' . $this->EMP_LastName);
    }

    /**
     * Get is checked in attribute
     */
    public function getIsCheckedInAttribute(): bool
    {
        return $this->isCheckedIn();
    }

    /**
     * Get formatted employee number
     */
    public function getFormattedEmpNoAttribute(): string
    {
        return $this->EMP_EmpNo ?: 'N/A';
    }

    /**
     * Get display name (full name with employee number)
     */
    public function getDisplayNameAttribute(): string
    {
        $fullName = $this->getFullNameAttribute();
        $empNo = $this->EMP_EmpNo;

        return $empNo ? "{$fullName} ({$empNo})" : $fullName;
    }

    /**
     * Get department display name
     */
    public function getDepartmentDisplayAttribute(): string
    {
        return $this->EMP_Department ?: 'Unassigned';
    }

    // ===================================
    // PHOTO MANAGEMENT
    // ===================================

    /**
     * Get employee photo URL
     */
    public function getPhotoUrlAttribute(): ?string
    {
        if (empty($this->EMP_PhotoPath)) {
            return $this->getDefaultPhotoUrl();
        }

        // If it's already a full URL, return as is
        if (filter_var($this->EMP_PhotoPath, FILTER_VALIDATE_URL)) {
            return $this->EMP_PhotoPath;
        }

        // Handle full storage path (storage\app\public\filename.jpg)
        if (strpos($this->EMP_PhotoPath, 'storage\\app\\public\\') === 0) {
            $filename = str_replace('storage\\app\\public\\', '', $this->EMP_PhotoPath);
            return $this->getPhotoAssetUrl($filename);
        }

        // Handle storage/app/public/ path (Unix style)
        if (strpos($this->EMP_PhotoPath, 'storage/app/public/') === 0) {
            $filename = str_replace('storage/app/public/', '', $this->EMP_PhotoPath);
            return $this->getPhotoAssetUrl($filename);
        }

        // If it's a relative path, convert to full URL
        return $this->getPhotoAssetUrl(ltrim($this->EMP_PhotoPath, '/'));
    }

    /**
     * Get photo asset URL with case-insensitive file checking
     */
    private function getPhotoAssetUrl(string $filename): string
    {
        $storagePath = storage_path('app/public');

        // First try exact match
        if (file_exists($storagePath . '/' . $filename)) {
            return asset('storage/' . $filename);
        }

        // Try case-insensitive match
        $files = glob($storagePath . '/' . strtolower($filename));
        if (empty($files)) {
            $files = glob($storagePath . '/' . strtoupper($filename));
        }

        if (empty($files)) {
            // Try with different case extensions
            $pathInfo = pathinfo($filename);
            $baseName = $pathInfo['filename'];
            $extension = $pathInfo['extension'] ?? '';

            if ($extension) {
                $upperExt = $baseName . '.' . strtoupper($extension);
                $lowerExt = $baseName . '.' . strtolower($extension);

                if (file_exists($storagePath . '/' . $upperExt)) {
                    return asset('storage/' . $upperExt);
                }
                if (file_exists($storagePath . '/' . $lowerExt)) {
                    return asset('storage/' . $lowerExt);
                }
            }

            // Return default photo if file doesn't exist
            return $this->getDefaultPhotoUrl();
        } else {
            $actualFilename = basename($files[0]);
            return asset('storage/' . $actualFilename);
        }
    }

    /**
     * Get default photo URL for employees without photos
     */
    private function getDefaultPhotoUrl(): string
    {
        // Generate a placeholder based on initials
        $initials = $this->getInitials();
        return "https://ui-avatars.com/api/?name={$initials}&size=200&background=6366f1&color=ffffff&bold=true";
    }

    /**
     * Get employee initials for photo placeholder
     */
    public function getInitials(): string
    {
        $firstName = $this->EMP_FirstName ?? '';
        $lastName = $this->EMP_LastName ?? '';

        $firstInitial = !empty($firstName) ? strtoupper(substr($firstName, 0, 1)) : '';
        $lastInitial = !empty($lastName) ? strtoupper(substr($lastName, 0, 1)) : '';

        return $firstInitial . $lastInitial ?: 'EM';
    }

    /**
     * Check if employee has a photo
     */
    public function hasPhoto(): bool
    {
        if (empty($this->EMP_PhotoPath)) {
            return false;
        }

        // If it's a URL, assume it exists
        if (filter_var($this->EMP_PhotoPath, FILTER_VALIDATE_URL)) {
            return true;
        }

        // Check if file exists in storage
        $storagePath = storage_path('app/public');
        $filename = basename($this->EMP_PhotoPath);

        return file_exists($storagePath . '/' . $filename);
    }

    /**
     * Validate photo file
     */
    public static function validatePhoto($file): array
    {
        $errors = [];

        if (!$file) {
            return $errors;
        }

        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            $errors[] = 'Photo file size must be less than 5MB';
        }

        // Check file type
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = 'Photo must be a valid image file (JPEG, PNG, GIF, or WebP)';
        }

        // Check image dimensions
        if ($file->isValid()) {
            $imageInfo = getimagesize($file->getPathname());
            if ($imageInfo) {
                $width = $imageInfo[0];
                $height = $imageInfo[1];

                // Minimum dimensions
                if ($width < 100 || $height < 100) {
                    $errors[] = 'Photo dimensions must be at least 100x100 pixels';
                }

                // Maximum dimensions
                if ($width > 2000 || $height > 2000) {
                    $errors[] = 'Photo dimensions must not exceed 2000x2000 pixels';
                }
            }
        }

        return $errors;
    }

    /**
     * Store employee photo
     */
    public function storePhoto($file): bool
    {
        $errors = self::validatePhoto($file);
        if (!empty($errors)) {
            return false;
        }

        try {
            // Generate unique filename
            $extension = $file->getClientOriginalExtension();
            $filename = 'employee_' . $this->EMP_EmpNo . '_' . time() . '.' . $extension;

            // Store the file
            $path = $file->storeAs('public', $filename);

            if ($path) {
                // Delete old photo if exists
                $this->deletePhoto();

                // Update photo path
                $this->EMP_PhotoPath = $filename;
                $this->save();

                return true;
            }
        } catch (\Exception $e) {
            \Log::error('Failed to store employee photo: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Delete employee photo
     */
    public function deletePhoto(): bool
    {
        if (empty($this->EMP_PhotoPath)) {
            return true;
        }

        try {
            $storagePath = storage_path('app/public/' . basename($this->EMP_PhotoPath));
            if (file_exists($storagePath)) {
                unlink($storagePath);
            }

            $this->EMP_PhotoPath = null;
            $this->save();

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to delete employee photo: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate barcode for this employee
     */
    public function generateBarcode($type = 'CODE128', $width = 2, $height = 100): string
    {
        // For now, return a simple barcode representation
        return $this->getSimpleBarcodeSVG($width, $height);
    }

    /**
     * Get barcode as PNG
     */
    public function getBarcodeImage($type = 'CODE128', $width = 2, $height = 100): string
    {
        // Return empty string for now - PNG generation requires more complex setup
        return '';
    }

    /**
     * Get barcode HTML for display
     */
    public function getBarcodeHtml($type = 'CODE128', $width = 2, $height = 100): string
    {
        return '<div style="text-align: center; padding: 20px; border: 1px solid #ddd; background: white;">' .
               $this->getSimpleBarcodeSVG($width, $height) .
               '</div>';
    }

    /**
     * Generate a simple SVG barcode representation
     * This creates a basic barcode-like SVG for display purposes
     */
    private function getSimpleBarcodeSVG(int $width = 2, int $height = 100): string
    {
        $bioId = $this->EMP_BioID;
        if (empty($bioId)) {
            return '';
        }

        // Create a simple barcode pattern based on the BioID
        $pattern = $this->generateBarcodePattern($bioId);
        $barWidth = $width;
        $totalWidth = count($pattern) * $barWidth;

        $svg = '<svg width="' . $totalWidth . '" height="' . ($height + 40) . '" xmlns="http://www.w3.org/2000/svg">';

        // Draw the barcode bars
        $x = 0;
        foreach ($pattern as $bar) {
            if ($bar === 1) {
                $svg .= '<rect x="' . $x . '" y="10" width="' . $barWidth . '" height="' . $height . '" fill="black"/>';
            }
            $x += $barWidth;
        }

        // Add the text below the barcode
        $textX = $totalWidth / 2;
        $textY = $height + 30;
        $svg .= '<text x="' . $textX . '" y="' . $textY . '" text-anchor="middle" font-family="monospace" font-size="12" fill="black">' . htmlspecialchars($bioId) . '</text>';

        $svg .= '</svg>';

        return $svg;
    }

    /**
     * Generate a barcode pattern array based on the BioID
     * Returns an array of 1s and 0s representing bars and spaces
     */
    private function generateBarcodePattern(string $bioId): array
    {
        $pattern = [];

        // Start pattern
        $pattern = array_merge($pattern, [1, 1, 0, 1, 0, 0, 0, 1, 1]);

        // Convert each character to a pattern
        foreach (str_split($bioId) as $char) {
            $charPattern = $this->getCharacterPattern($char);
            $pattern = array_merge($pattern, $charPattern);
        }

        // End pattern
        $pattern = array_merge($pattern, [1, 1, 0, 0, 0, 1, 0, 1, 1]);

        return $pattern;
    }

    /**
     * Get barcode pattern for a specific character
     */
    private function getCharacterPattern(string $char): array
    {
        // Simple pattern mapping for alphanumeric characters
        $patterns = [
            '0' => [0, 0, 0, 1, 1, 0, 1, 0, 0],
            '1' => [0, 0, 1, 0, 0, 1, 1, 0, 0],
            '2' => [0, 0, 1, 0, 0, 0, 1, 1, 0],
            '3' => [0, 1, 1, 1, 1, 0, 0, 0, 0],
            '4' => [0, 0, 0, 1, 0, 1, 1, 0, 0],
            '5' => [0, 1, 0, 0, 0, 1, 1, 0, 0],
            '6' => [0, 1, 0, 0, 0, 0, 1, 1, 0],
            '7' => [0, 1, 0, 1, 1, 0, 0, 0, 0],
            '8' => [0, 0, 0, 1, 0, 0, 1, 1, 0],
            '9' => [0, 0, 1, 0, 1, 1, 0, 0, 0],
            'A' => [1, 0, 0, 0, 0, 1, 1, 0, 0],
            'B' => [1, 0, 0, 1, 0, 0, 1, 0, 0],
            'C' => [1, 0, 0, 1, 0, 0, 0, 1, 0],
            'D' => [1, 0, 0, 0, 1, 0, 1, 0, 0],
            'E' => [1, 1, 0, 0, 0, 0, 1, 0, 0],
            'F' => [1, 1, 0, 1, 0, 0, 0, 0, 0],
            'G' => [1, 0, 1, 0, 0, 0, 1, 0, 0],
            'H' => [1, 0, 1, 0, 0, 1, 0, 0, 0],
            'I' => [1, 0, 1, 0, 1, 0, 0, 0, 0],
            'J' => [1, 0, 0, 0, 1, 0, 0, 1, 0],
            'K' => [1, 1, 0, 0, 0, 1, 0, 0, 0],
            'L' => [1, 1, 0, 0, 1, 0, 0, 0, 0],
            'M' => [1, 0, 1, 1, 0, 0, 0, 0, 0],
            'N' => [1, 0, 0, 0, 1, 1, 0, 0, 0],
            'O' => [1, 0, 0, 1, 1, 0, 0, 0, 0],
            'P' => [1, 1, 0, 0, 1, 1, 0, 0, 0],
        ];

        return $patterns[strtoupper($char)] ?? [0, 1, 0, 1, 0, 1, 0, 1, 0]; // Default pattern
    }
}
