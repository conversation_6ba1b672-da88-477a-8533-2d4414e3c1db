<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Employee;

/**
 * Update Employee Request
 * 
 * Handles validation for updating existing employees with comprehensive
 * validation rules and custom error messages.
 * 
 * @package App\Http\Requests
 */
class UpdateEmployeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Add authorization logic here if needed
        // For now, allow all authenticated users
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $employee = $this->route('employee');
        $employeeId = $employee instanceof Employee ? $employee->EMP_EmpID : $employee;

        return [
            'EMP_EmpNo' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('Employee', 'EMP_EmpNo')->ignore($employeeId, 'EMP_EmpID'),
                'regex:/^[A-Z0-9]+$/', // Only uppercase letters and numbers
            ],
            'EMP_BioID' => [
                'nullable',
                'string',
                'max:20',
                Rule::unique('Employee', 'EMP_BioID')->ignore($employeeId, 'EMP_EmpID'),
                'regex:/^EMP\d{5}$/', // Must match EMP + 5 digits format
            ],
            'EMP_FirstName' => [
                'required',
                'string',
                'max:100',
                'min:2',
                'regex:/^[a-zA-Z\s\-\'\.]+$/', // Only letters, spaces, hyphens, apostrophes, dots
            ],
            'EMP_LastName' => [
                'required',
                'string',
                'max:100',
                'min:2',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'EMP_MiddleName' => [
                'nullable',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'EMP_FullName' => [
                'nullable',
                'string',
                'max:255',
            ],
            'EMP_Department' => [
                'nullable',
                'string',
                'max:100',
                Rule::in([
                    'IT Department',
                    'Human Resources',
                    'Finance',
                    'Marketing',
                    'Operations',
                    'Sales',
                    'Administration',
                    'Customer Service',
                    'Research & Development',
                    'Quality Assurance',
                ]),
            ],
            'EMP_IsActive' => [
                'required',
                'integer',
                'in:0,1',
            ],
            'EMP_Type' => [
                'nullable',
                'string',
                'max:50',
                Rule::in([
                    'full_time',
                    'part_time',
                    'contract',
                    'intern',
                    'consultant',
                    'temporary',
                ]),
            ],
            'EMP_PhotoPath' => [
                'nullable',
                'string',
                'max:500',
            ],
            'photo' => [
                'nullable',
                'image',
                'mimes:jpeg,jpg,png,gif,webp',
                'max:5120', // 5MB in kilobytes
                'dimensions:min_width=100,min_height=100,max_width=2000,max_height=2000',
            ],
            'delete_photo' => [
                'nullable',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'EMP_EmpNo.unique' => 'This employee number is already taken by another employee.',
            'EMP_EmpNo.regex' => 'Employee number must contain only uppercase letters and numbers.',
            'EMP_BioID.unique' => 'This barcode ID is already in use by another employee.',
            'EMP_BioID.regex' => 'Barcode ID must be in format EMP followed by 5 digits (e.g., EMP12345).',
            'EMP_FirstName.required' => 'First name is required.',
            'EMP_FirstName.min' => 'First name must be at least 2 characters.',
            'EMP_FirstName.regex' => 'First name can only contain letters, spaces, hyphens, apostrophes, and dots.',
            'EMP_LastName.required' => 'Last name is required.',
            'EMP_LastName.min' => 'Last name must be at least 2 characters.',
            'EMP_LastName.regex' => 'Last name can only contain letters, spaces, hyphens, apostrophes, and dots.',
            'EMP_MiddleName.regex' => 'Middle name can only contain letters, spaces, hyphens, apostrophes, and dots.',
            'EMP_Department.in' => 'Please select a valid department.',
            'EMP_IsActive.required' => 'Employee status is required.',
            'EMP_IsActive.in' => 'Employee status must be either active (1) or inactive (0).',
            'EMP_Type.in' => 'Please select a valid employee type.',
            'photo.image' => 'The uploaded file must be an image.',
            'photo.mimes' => 'Photo must be a JPEG, PNG, GIF, or WebP image.',
            'photo.max' => 'Photo file size must not exceed 5MB.',
            'photo.dimensions' => 'Photo dimensions must be between 100x100 and 2000x2000 pixels.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'EMP_EmpNo' => 'employee number',
            'EMP_BioID' => 'barcode ID',
            'EMP_FirstName' => 'first name',
            'EMP_LastName' => 'last name',
            'EMP_MiddleName' => 'middle name',
            'EMP_FullName' => 'full name',
            'EMP_Department' => 'department',
            'EMP_IsActive' => 'status',
            'EMP_Type' => 'employee type',
            'EMP_PhotoPath' => 'photo path',
            'photo' => 'photo',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Auto-generate full name if not provided
        if (!$this->filled('EMP_FullName')) {
            $fullName = trim(
                ($this->EMP_FirstName ?? '') . ' ' .
                ($this->EMP_MiddleName ?? '') . ' ' .
                ($this->EMP_LastName ?? '')
            );
            
            if (!empty($fullName)) {
                $this->merge(['EMP_FullName' => $fullName]);
            }
        }

        // Normalize employee number to uppercase
        if ($this->filled('EMP_EmpNo')) {
            $this->merge(['EMP_EmpNo' => strtoupper($this->EMP_EmpNo)]);
        }

        // Normalize names (proper case)
        foreach (['EMP_FirstName', 'EMP_LastName', 'EMP_MiddleName'] as $field) {
            if ($this->filled($field)) {
                $this->merge([$field => ucwords(strtolower($this->$field))]);
            }
        }
    }

    /**
     * Handle a passed validation attempt.
     */
    protected function passedValidation(): void
    {
        // Additional processing after validation passes
        // This could include logging, notifications, etc.
    }
}
