<?php

namespace App\Jobs;

use App\Services\AttendanceService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * Generate Attendance Report Job
 * 
 * Background job to generate attendance reports in various formats.
 * This job handles heavy report generation tasks asynchronously.
 * 
 * @package App\Jobs
 */
class GenerateAttendanceReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public array $filters;
    public string $format;
    public string $reportType;
    public ?string $userEmail;
    public string $reportId;

    /**
     * Create a new job instance.
     */
    public function __construct(
        array $filters,
        string $format = 'csv',
        string $reportType = 'consolidated',
        ?string $userEmail = null
    ) {
        $this->filters = $filters;
        $this->format = $format;
        $this->reportType = $reportType;
        $this->userEmail = $userEmail;
        $this->reportId = uniqid('report_', true);
    }

    /**
     * Execute the job.
     */
    public function handle(AttendanceService $attendanceService): void
    {
        Log::info('Starting attendance report generation', [
            'report_id' => $this->reportId,
            'format' => $this->format,
            'report_type' => $this->reportType,
            'filters' => $this->filters,
        ]);

        try {
            $reportData = $this->generateReportData($attendanceService);
            $filePath = $this->saveReportFile($reportData);
            
            Log::info('Attendance report generated successfully', [
                'report_id' => $this->reportId,
                'file_path' => $filePath,
                'records_count' => count($reportData),
            ]);

            // Notify user if email provided
            if ($this->userEmail) {
                $this->notifyUser($filePath);
            }

        } catch (\Exception $e) {
            Log::error('Failed to generate attendance report', [
                'report_id' => $this->reportId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Generate report data based on type
     */
    private function generateReportData(AttendanceService $attendanceService): array
    {
        switch ($this->reportType) {
            case 'consolidated':
                return $this->generateConsolidatedReport($attendanceService);
            case 'detailed':
                return $this->generateDetailedReport($attendanceService);
            case 'summary':
                return $this->generateSummaryReport($attendanceService);
            case 'sessions':
                return $this->generateSessionsReport($attendanceService);
            default:
                throw new \InvalidArgumentException("Unknown report type: {$this->reportType}");
        }
    }

    /**
     * Generate consolidated hours report
     */
    private function generateConsolidatedReport(AttendanceService $attendanceService): array
    {
        $sessions = $attendanceService->generateMergedSessions($this->filters);
        return $attendanceService->generateConsolidatedHours($sessions);
    }

    /**
     * Generate detailed attendance report
     */
    private function generateDetailedReport(AttendanceService $attendanceService): array
    {
        // This would include all attendance logs with full details
        return $attendanceService->generateMergedSessions($this->filters);
    }

    /**
     * Generate summary report
     */
    private function generateSummaryReport(AttendanceService $attendanceService): array
    {
        return $attendanceService->generateHoursWorkedSummary($this->filters);
    }

    /**
     * Generate sessions report
     */
    private function generateSessionsReport(AttendanceService $attendanceService): array
    {
        return $attendanceService->generateMergedSessions($this->filters);
    }

    /**
     * Save report file to storage
     */
    private function saveReportFile(array $reportData): string
    {
        $filename = $this->generateFilename();
        $content = $this->formatReportContent($reportData);
        
        $filePath = "reports/attendance/{$filename}";
        Storage::disk('local')->put($filePath, $content);
        
        return $filePath;
    }

    /**
     * Generate filename for the report
     */
    private function generateFilename(): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $dateRange = '';
        
        if (isset($this->filters['date_from']) && isset($this->filters['date_to'])) {
            $dateRange = "_{$this->filters['date_from']}_to_{$this->filters['date_to']}";
        }
        
        return "attendance_{$this->reportType}{$dateRange}_{$timestamp}.{$this->format}";
    }

    /**
     * Format report content based on format
     */
    private function formatReportContent(array $reportData): string
    {
        switch ($this->format) {
            case 'csv':
                return $this->formatAsCsv($reportData);
            case 'json':
                return json_encode($reportData, JSON_PRETTY_PRINT);
            case 'xml':
                return $this->formatAsXml($reportData);
            default:
                throw new \InvalidArgumentException("Unsupported format: {$this->format}");
        }
    }

    /**
     * Format data as CSV
     */
    private function formatAsCsv(array $reportData): string
    {
        if (empty($reportData)) {
            return '';
        }

        $output = fopen('php://temp', 'r+');
        
        // Add header with date range info
        $dateRange = $this->getDateRangeString();
        fputcsv($output, [$dateRange]);
        fputcsv($output, []); // Empty row
        
        // Add CSV headers based on report type
        $headers = $this->getCsvHeaders();
        fputcsv($output, $headers);
        
        // Add data rows
        foreach ($reportData as $row) {
            $csvRow = $this->formatRowForCsv($row);
            fputcsv($output, $csvRow);
        }
        
        rewind($output);
        $content = stream_get_contents($output);
        fclose($output);
        
        return $content;
    }

    /**
     * Format data as XML
     */
    private function formatAsXml(array $reportData): string
    {
        $xml = new \SimpleXMLElement('<attendance_report/>');
        $xml->addAttribute('generated_at', now()->toISOString());
        $xml->addAttribute('report_type', $this->reportType);
        
        // Add metadata
        $metadata = $xml->addChild('metadata');
        $metadata->addChild('date_range', $this->getDateRangeString());
        $metadata->addChild('total_records', count($reportData));
        
        // Add data
        $data = $xml->addChild('data');
        foreach ($reportData as $index => $row) {
            $item = $data->addChild('item');
            $this->arrayToXml($row, $item);
        }
        
        return $xml->asXML();
    }

    /**
     * Convert array to XML recursively
     */
    private function arrayToXml(array $array, \SimpleXMLElement $xml): void
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $child = $xml->addChild($key);
                $this->arrayToXml($value, $child);
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
    }

    /**
     * Get CSV headers based on report type
     */
    private function getCsvHeaders(): array
    {
        switch ($this->reportType) {
            case 'consolidated':
                return [
                    'Employee Name',
                    'Employee ID',
                    'Total Hours Worked',
                    'Total Sessions',
                    'Working Days',
                    'Average Hours per Day'
                ];
            case 'sessions':
                return [
                    'Employee Name',
                    'Employee ID',
                    'Date',
                    'Check In',
                    'Check Out',
                    'Duration (Hours)',
                    'Status'
                ];
            default:
                return ['Data'];
        }
    }

    /**
     * Format row for CSV based on report type
     */
    private function formatRowForCsv(array $row): array
    {
        switch ($this->reportType) {
            case 'consolidated':
                return [
                    $row['employee']->EMP_FullName ?? 'Unknown',
                    $row['employee']->EMP_EmpNo ?? '',
                    round($row['total_hours'], 2),
                    $row['session_count'],
                    $row['working_days'],
                    round($row['average_hours_per_day'], 2)
                ];
            case 'sessions':
                return [
                    $row['employee']->EMP_FullName ?? 'Unknown',
                    $row['employee']->EMP_EmpNo ?? '',
                    $row['date'],
                    $row['check_in'] ? $row['check_in']->logged_at->format('H:i') : '',
                    $row['check_out'] ? $row['check_out']->logged_at->format('H:i') : '',
                    $row['duration_hours'],
                    $row['is_complete'] ? 'Complete' : 'Incomplete'
                ];
            default:
                return [json_encode($row)];
        }
    }

    /**
     * Get date range string for report header
     */
    private function getDateRangeString(): string
    {
        if (isset($this->filters['date_from']) && isset($this->filters['date_to'])) {
            return "Date Range: {$this->filters['date_from']} to {$this->filters['date_to']}";
        } elseif (isset($this->filters['date_from'])) {
            return "From: {$this->filters['date_from']}";
        } elseif (isset($this->filters['date_to'])) {
            return "Until: {$this->filters['date_to']}";
        } else {
            return "Date Range: Last 7 days";
        }
    }

    /**
     * Notify user about report completion
     */
    private function notifyUser(string $filePath): void
    {
        // Here you would send an email notification
        // with download link or attach the report file
        Log::info('Report ready for user', [
            'report_id' => $this->reportId,
            'user_email' => $this->userEmail,
            'file_path' => $filePath,
        ]);
    }

    /**
     * The job failed to process.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Attendance report generation failed', [
            'report_id' => $this->reportId,
            'error' => $exception->getMessage(),
        ]);

        // Notify user about failure if email provided
        if ($this->userEmail) {
            Log::info('Notifying user about report generation failure', [
                'user_email' => $this->userEmail,
                'report_id' => $this->reportId,
            ]);
        }
    }
}
