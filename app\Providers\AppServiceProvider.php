<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Employee;
use App\Observers\EmployeeObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Attendance Repository
        $this->app->bind(
            \App\Repositories\AttendanceRepository::class,
            \App\Repositories\AttendanceRepository::class
        );

        // Register Attendance Service
        $this->app->bind(
            \App\Services\AttendanceService::class,
            \App\Services\AttendanceService::class
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register model observers
        Employee::observe(EmployeeObserver::class);

        // Register Event Listeners
        \Event::listen(
            \App\Events\AttendanceRecorded::class,
            \App\Listeners\HandleAttendanceRecorded::class
        );
    }
}
