<?php

namespace App\Jobs;

use App\Models\AttendanceLog;
use App\Models\Employee;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * Process Attendance Notification Job
 * 
 * Background job to handle attendance-related notifications
 * such as late arrivals, overtime alerts, or missing check-outs.
 * 
 * @package App\Jobs
 */
class ProcessAttendanceNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public AttendanceLog $attendanceLog;
    public Employee $employee;
    public string $notificationType;
    public array $additionalData;

    /**
     * Create a new job instance.
     */
    public function __construct(
        AttendanceLog $attendanceLog,
        Employee $employee,
        string $notificationType,
        array $additionalData = []
    ) {
        $this->attendanceLog = $attendanceLog;
        $this->employee = $employee;
        $this->notificationType = $notificationType;
        $this->additionalData = $additionalData;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Processing attendance notification', [
            'employee' => $this->employee->EMP_FullName,
            'notification_type' => $this->notificationType,
            'attendance_log_id' => $this->attendanceLog->id,
        ]);

        try {
            switch ($this->notificationType) {
                case 'late_arrival':
                    $this->handleLateArrival();
                    break;
                case 'overtime_alert':
                    $this->handleOvertimeAlert();
                    break;
                case 'missing_checkout':
                    $this->handleMissingCheckout();
                    break;
                case 'early_departure':
                    $this->handleEarlyDeparture();
                    break;
                case 'long_break':
                    $this->handleLongBreak();
                    break;
                default:
                    Log::warning('Unknown notification type', [
                        'type' => $this->notificationType
                    ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to process attendance notification', [
                'employee' => $this->employee->EMP_FullName,
                'notification_type' => $this->notificationType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e; // Re-throw to trigger job retry
        }
    }

    /**
     * Handle late arrival notification
     */
    private function handleLateArrival(): void
    {
        $lateMinutes = $this->additionalData['late_minutes'] ?? 0;
        
        Log::info('Employee late arrival detected', [
            'employee' => $this->employee->EMP_FullName,
            'late_minutes' => $lateMinutes,
            'check_in_time' => $this->attendanceLog->logged_at->format('H:i'),
        ]);

        // Here you could:
        // - Send email to HR
        // - Send notification to manager
        // - Update employee record
        // - Trigger disciplinary workflow
    }

    /**
     * Handle overtime alert notification
     */
    private function handleOvertimeAlert(): void
    {
        $overtimeHours = $this->additionalData['overtime_hours'] ?? 0;
        
        Log::info('Employee overtime detected', [
            'employee' => $this->employee->EMP_FullName,
            'overtime_hours' => $overtimeHours,
            'total_hours' => $this->additionalData['total_hours'] ?? 0,
        ]);

        // Here you could:
        // - Send notification to manager for approval
        // - Calculate overtime pay
        // - Update payroll system
        // - Send alert to HR
    }

    /**
     * Handle missing checkout notification
     */
    private function handleMissingCheckout(): void
    {
        Log::warning('Missing checkout detected', [
            'employee' => $this->employee->EMP_FullName,
            'last_check_in' => $this->attendanceLog->logged_at->format('Y-m-d H:i'),
        ]);

        // Here you could:
        // - Send reminder to employee
        // - Notify supervisor
        // - Auto-checkout after certain hours
        // - Flag for manual review
    }

    /**
     * Handle early departure notification
     */
    private function handleEarlyDeparture(): void
    {
        $earlyMinutes = $this->additionalData['early_minutes'] ?? 0;
        
        Log::info('Early departure detected', [
            'employee' => $this->employee->EMP_FullName,
            'early_minutes' => $earlyMinutes,
            'checkout_time' => $this->attendanceLog->logged_at->format('H:i'),
        ]);

        // Here you could:
        // - Require manager approval
        // - Deduct from leave balance
        // - Send notification to HR
        // - Update attendance record
    }

    /**
     * Handle long break notification
     */
    private function handleLongBreak(): void
    {
        $breakMinutes = $this->additionalData['break_minutes'] ?? 0;
        
        Log::info('Long break detected', [
            'employee' => $this->employee->EMP_FullName,
            'break_minutes' => $breakMinutes,
        ]);

        // Here you could:
        // - Send reminder notification
        // - Notify supervisor
        // - Track break time violations
        // - Update attendance policies
    }

    /**
     * The job failed to process.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Attendance notification job failed', [
            'employee' => $this->employee->EMP_FullName,
            'notification_type' => $this->notificationType,
            'attendance_log_id' => $this->attendanceLog->id,
            'error' => $exception->getMessage(),
        ]);

        // Here you could:
        // - Send alert to system administrators
        // - Store failed notification for manual processing
        // - Trigger fallback notification method
    }
}
