<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

/**
 * Attendance Logs Request
 * 
 * Handles validation for attendance logs filtering and retrieval
 * with comprehensive validation rules and custom error messages.
 * 
 * @package App\Http\Requests
 */
class AttendanceLogsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'date_from' => [
                'nullable',
                'date',
                'before_or_equal:today',
                'before_or_equal:date_to',
            ],
            'date_to' => [
                'nullable',
                'date',
                'before_or_equal:today',
                'after_or_equal:date_from',
            ],
            'employees' => [
                'nullable',
                'array',
                'max:50', // Limit to 50 employees for performance
            ],
            'employees.*' => [
                'required',
                function ($attribute, $value, $fail) {
                    // Check if employee exists with this EMP_EmpNo
                    $employee = \App\Models\Employee::where('EMP_EmpNo', $value)->first();
                    if (!$employee) {
                        $fail("Employee with ID {$value} does not exist.");
                    }
                },
            ],
            'action_type' => [
                'nullable',
                'integer',
                Rule::in([1, 2, 3, 4]), // 1=check_in, 2=check_out, 3=break_in, 4=break_out
            ],
            'department' => [
                'nullable',
                'string',
                'max:100',
            ],
            'per_page' => [
                'nullable',
                'integer',
                'min:10',
                'max:500',
            ],
            'export_format' => [
                'nullable',
                'string',
                Rule::in(['csv', 'excel', 'pdf']),
            ],
            'view_type' => [
                'nullable',
                'string',
                Rule::in(['logs', 'sessions', 'summary']),
            ],
            'sort_by' => [
                'nullable',
                'string',
                Rule::in(['logged_at', 'employee_name', 'action_type', 'department']),
            ],
            'sort_direction' => [
                'nullable',
                'string',
                Rule::in(['asc', 'desc']),
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'date_from.date' => 'From date must be a valid date.',
            'date_from.before_or_equal' => 'From date cannot be in the future.',
            'date_to.date' => 'To date must be a valid date.',
            'date_to.before_or_equal' => 'To date cannot be in the future.',
            'date_to.after_or_equal' => 'To date must be after or equal to from date.',
            'employees.array' => 'Employees must be a valid list.',
            'employees.max' => 'You can select a maximum of 50 employees.',
            'employees.*.exists' => 'One or more selected employees do not exist.',
            'action_type.in' => 'Action type must be a valid attendance action.',
            'department.max' => 'Department name must not exceed 100 characters.',
            'per_page.min' => 'Items per page must be at least 10.',
            'per_page.max' => 'Items per page cannot exceed 500.',
            'export_format.in' => 'Export format must be CSV, Excel, or PDF.',
            'view_type.in' => 'View type must be logs, sessions, or summary.',
            'sort_by.in' => 'Sort field must be a valid column.',
            'sort_direction.in' => 'Sort direction must be ascending or descending.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'date_from' => 'from date',
            'date_to' => 'to date',
            'employees' => 'employees',
            'action_type' => 'action type',
            'department' => 'department',
            'per_page' => 'items per page',
            'export_format' => 'export format',
            'view_type' => 'view type',
            'sort_by' => 'sort field',
            'sort_direction' => 'sort direction',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Debug logging to see what we're receiving
        \Log::info('AttendanceLogsRequest received data:', [
            'employees_raw' => $this->employees,
            'employees_type' => gettype($this->employees),
            'all_data' => $this->all()
        ]);

        // Set default values
        $defaults = [
            'per_page' => 100,
            'view_type' => 'logs',
            'sort_by' => 'logged_at',
            'sort_direction' => 'desc',
        ];

        foreach ($defaults as $key => $value) {
            if (!$this->filled($key)) {
                $this->merge([$key => $value]);
            }
        }

        // Normalize date formats
        if ($this->filled('date_from')) {
            try {
                $dateFrom = Carbon::parse($this->date_from)->format('Y-m-d');
                $this->merge(['date_from' => $dateFrom]);
            } catch (\Exception $e) {
                // Let validation handle the error
            }
        }

        if ($this->filled('date_to')) {
            try {
                $dateTo = Carbon::parse($this->date_to)->format('Y-m-d');
                $this->merge(['date_to' => $dateTo]);
            } catch (\Exception $e) {
                // Let validation handle the error
            }
        }

        // Ensure employees is an array and handle JSON strings
        if ($this->filled('employees')) {
            $employees = $this->employees;

            // Handle JSON string (e.g., '["502"]')
            if (is_string($employees)) {
                $decoded = json_decode($employees, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $employees = $decoded;
                } else {
                    // Handle comma-separated string
                    $employees = explode(',', $employees);
                }
            }

            // Ensure it's an array
            if (!is_array($employees)) {
                $employees = [$employees];
            }

            // Filter out empty values and ensure numeric strings
            $employees = array_filter(array_map('trim', $employees), function($emp) {
                return !empty($emp) && (is_numeric($emp) || is_string($emp));
            });

            $this->merge(['employees' => array_values($employees)]);

            // Debug logging after transformation
            \Log::info('AttendanceLogsRequest employees after transformation:', [
                'employees' => array_values($employees),
                'count' => count($employees)
            ]);
        }

        // Normalize department
        if ($this->filled('department')) {
            $this->merge(['department' => trim($this->department)]);
        }
    }

    /**
     * Get the validated data with additional processing.
     */
    public function getValidatedData(): array
    {
        $validated = $this->validated();
        
        // Set default date range if not provided (last 7 days)
        if (empty($validated['date_from']) && empty($validated['date_to'])) {
            $validated['date_from'] = now()->subDays(7)->format('Y-m-d');
            $validated['date_to'] = now()->format('Y-m-d');
        }
        
        return $validated;
    }

    /**
     * Get filters array for repository/service
     */
    public function getFilters(): array
    {
        $validated = $this->getValidatedData();
        
        return [
            'date_from' => $validated['date_from'] ?? null,
            'date_to' => $validated['date_to'] ?? null,
            'employees' => $validated['employees'] ?? [],
            'action_type' => $validated['action_type'] ?? null,
            'department' => $validated['department'] ?? null,
        ];
    }

    /**
     * Get pagination options
     */
    public function getPaginationOptions(): array
    {
        return [
            'per_page' => $this->validated()['per_page'] ?? 100,
            'sort_by' => $this->validated()['sort_by'] ?? 'logged_at',
            'sort_direction' => $this->validated()['sort_direction'] ?? 'desc',
        ];
    }

    /**
     * Check if this is an export request
     */
    public function isExportRequest(): bool
    {
        return $this->filled('export_format') || $this->routeIs('*.export*');
    }

    /**
     * Get export format
     */
    public function getExportFormat(): string
    {
        return $this->validated()['export_format'] ?? 'csv';
    }

    /**
     * Handle a passed validation attempt.
     */
    protected function passedValidation(): void
    {
        // Log successful validation for audit purposes
        \Log::info('Attendance logs request validated', [
            'filters' => $this->getFilters(),
            'pagination' => $this->getPaginationOptions(),
            'is_export' => $this->isExportRequest(),
            'user_ip' => $this->ip(),
        ]);
    }
}
