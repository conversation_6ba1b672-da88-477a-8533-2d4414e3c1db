<?php

namespace App\Repositories;

use App\Models\AttendanceLog;
use App\Models\Employee;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

/**
 * Attendance Repository
 * 
 * Handles all data access operations for attendance-related functionality.
 * This layer abstracts database queries and provides a clean interface
 * for the service layer.
 * 
 * @package App\Repositories
 */
class AttendanceRepository
{
    /**
     * Find employee by BioID
     */
    public function findEmployeeByBioId(string $bioId): ?Employee
    {
        return Employee::where('EMP_BioID', $bioId)->first();
    }

    /**
     * Find employee by employee number
     */
    public function findEmployeeByEmpNo(string $empNo): ?Employee
    {
        return Employee::where('EMP_EmpNo', $empNo)->first();
    }

    /**
     * Get employee's last attendance action
     */
    public function getLastAttendanceAction(Employee $employee): ?AttendanceLog
    {
        return $employee->attendanceLogs()
            ->latest('logged_at')
            ->first();
    }

    /**
     * Create attendance log entry
     */
    public function createAttendanceLog(array $data): AttendanceLog
    {
        return AttendanceLog::create([
            'EMP_EmpNo' => $data['emp_no'],
            'EMP_BioID' => $data['bio_id'],
            'action_type' => $data['action_type'],
            'logged_at' => $data['logged_at'] ?? now(),
        ]);
    }

    /**
     * Get attendance logs with filtering and pagination
     */
    public function getAttendanceLogs(array $filters = [], int $perPage = 100): LengthAwarePaginator
    {
        $query = AttendanceLog::with('employee');

        // Apply filters
        $this->applyFilters($query, $filters);

        return $query->orderBy('logged_at', 'desc')
                    ->paginate($perPage)
                    ->withQueryString();
    }

    /**
     * Get attendance logs for a specific employee
     */
    public function getEmployeeAttendanceLogs(Employee $employee, array $filters = [], int $perPage = 100): LengthAwarePaginator
    {
        $query = $employee->attendanceLogs();

        // Apply date filtering
        if (isset($filters['start_date'])) {
            $clarionStartDate = AttendanceLog::toClarionDate(Carbon::parse($filters['start_date']));
            $query->where('log_date', '>=', $clarionStartDate);
        }

        if (isset($filters['end_date'])) {
            $clarionEndDate = AttendanceLog::toClarionDate(Carbon::parse($filters['end_date']));
            $query->where('log_date', '<=', $clarionEndDate);
        }

        // Default to last 30 days if no date filter
        if (!isset($filters['start_date']) && !isset($filters['end_date'])) {
            $clarionThirtyDaysAgo = AttendanceLog::toClarionDate(now()->subDays(30));
            $query->where('log_date', '>=', $clarionThirtyDaysAgo);
        }

        return $query->orderBy('logged_at', 'desc')
                    ->paginate($perPage)
                    ->withQueryString();
    }

    /**
     * Get attendance logs for session generation
     */
    public function getLogsForSessions(array $filters = []): Collection
    {
        $query = AttendanceLog::with('employee')
                             ->whereIn('action_type', [1, 2]); // Only check-in/check-out

        $this->applyFilters($query, $filters);

        return $query->orderBy('EMP_EmpNo')
                    ->orderBy('logged_at')
                    ->get();
    }

    /**
     * Get active employees for filtering
     */
    public function getActiveEmployees(): Collection
    {
        return Employee::active()
                      ->orderBy('EMP_FirstName')
                      ->get(['EMP_EmpNo', 'EMP_FullName', 'EMP_PhotoPath']);
    }

    /**
     * Get employee's today logs
     */
    public function getTodayLogs(Employee $employee): Collection
    {
        $clarionToday = AttendanceLog::toClarionDate(today());
        return $employee->attendanceLogs()
            ->where('log_date', $clarionToday)
            ->orderBy('logged_at')
            ->get();
    }

    /**
     * Check for duplicate attendance within time window
     */
    public function hasDuplicateAttendance(Employee $employee, int $actionType, int $minutesWindow = 5): bool
    {
        $timeThreshold = now()->subMinutes($minutesWindow);
        
        return AttendanceLog::where('EMP_EmpNo', $employee->EMP_EmpNo)
            ->where('action_type', $actionType)
            ->where('logged_at', '>=', $timeThreshold)
            ->exists();
    }

    /**
     * Get attendance statistics for employee
     */
    public function getEmployeeAttendanceStats(Employee $employee, Carbon $startDate, Carbon $endDate): array
    {
        $clarionStartDate = AttendanceLog::toClarionDate($startDate);
        $clarionEndDate = AttendanceLog::toClarionDate($endDate);

        $logs = $employee->attendanceLogs()
            ->whereBetween('log_date', [$clarionStartDate, $clarionEndDate])
            ->orderBy('logged_at')
            ->get();

        return [
            'total_logs' => $logs->count(),
            'check_ins' => $logs->where('action_type', 1)->count(),
            'check_outs' => $logs->where('action_type', 2)->count(),
            'break_ins' => $logs->where('action_type', 3)->count(),
            'break_outs' => $logs->where('action_type', 4)->count(),
            'first_log' => $logs->first(),
            'last_log' => $logs->last(),
        ];
    }

    /**
     * Apply filters to attendance logs query
     */
    private function applyFilters($query, array $filters): void
    {
        // Action type filter
        if (isset($filters['action_types']) && is_array($filters['action_types'])) {
            $query->whereIn('action_type', $filters['action_types']);
        } elseif (isset($filters['action_type'])) {
            $query->where('action_type', $filters['action_type']);
        } else {
            // Default to check-in/check-out only
            $query->whereIn('action_type', [1, 2]);
        }

        // Date range filtering
        if (isset($filters['date_from'])) {
            $clarionDateFrom = AttendanceLog::toClarionDate(Carbon::parse($filters['date_from']));
            $query->where('log_date', '>=', $clarionDateFrom);
        }

        if (isset($filters['date_to'])) {
            $clarionDateTo = AttendanceLog::toClarionDate(Carbon::parse($filters['date_to']));
            $query->where('log_date', '<=', $clarionDateTo);
        }

        // Default to last 7 days if no date range specified
        if (!isset($filters['date_from']) && !isset($filters['date_to'])) {
            $clarionSevenDaysAgo = AttendanceLog::toClarionDate(now()->subDays(7));
            $clarionToday = AttendanceLog::toClarionDate(today());
            $query->whereBetween('log_date', [$clarionSevenDaysAgo, $clarionToday]);
        }

        // Employee filtering
        if (isset($filters['employees']) && is_array($filters['employees'])) {
            $validEmployees = array_filter($filters['employees'], 'is_numeric');
            if (!empty($validEmployees)) {
                $query->whereIn('EMP_EmpNo', $validEmployees);
            }
        }
    }

    /**
     * Get attendance logs count by date range
     */
    public function getAttendanceCountByDateRange(Carbon $startDate, Carbon $endDate): int
    {
        $clarionStartDate = AttendanceLog::toClarionDate($startDate);
        $clarionEndDate = AttendanceLog::toClarionDate($endDate);

        return AttendanceLog::whereBetween('log_date', [$clarionStartDate, $clarionEndDate])
            ->count();
    }

    /**
     * Get most recent attendance logs for dashboard
     */
    public function getRecentAttendanceLogs(int $limit = 10): Collection
    {
        return AttendanceLog::with('employee')
            ->whereIn('action_type', [1, 2]) // Check-in/Check-out only
            ->latest('logged_at')
            ->limit($limit)
            ->get();
    }

    /**
     * Bulk create attendance logs (for imports/migrations)
     */
    public function bulkCreateAttendanceLogs(array $logsData): bool
    {
        try {
            DB::beginTransaction();
            
            foreach ($logsData as $logData) {
                $this->createAttendanceLog($logData);
            }
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
