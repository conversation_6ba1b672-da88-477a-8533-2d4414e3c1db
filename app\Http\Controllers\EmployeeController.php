<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Http\Requests\StoreEmployeeRequest;
use App\Http\Requests\UpdateEmployeeRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EmployeeController extends Controller
{
   
    public function index(Request $request)
    {
        $query = Employee::query();

        // Search functionality using the model scope
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by status using model scopes
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->byDepartment($request->department);
        }

        $employees = $query->orderBy('EMP_FirstName')
                          ->paginate(20)
                          ->withQueryString();

        // Get available departments for filter dropdown
        $departments = Employee::select('EMP_Department')
            ->whereNotNull('EMP_Department')
            ->where('EMP_Department', '!=', '')
            ->distinct()
            ->orderBy('EMP_Department')
            ->pluck('EMP_Department');

        return Inertia::render('Employees/Index', [
            'employees' => $employees,
            'filters' => $request->only(['search', 'status', 'department']),
            'departments' => $departments,
        ]);
    }

   
    public function create()
    {
        return Inertia::render('Employees/Create');
    }

  
    public function store(StoreEmployeeRequest $request)
    {
        $validated = $request->validated();

        // Handle photo upload if provided
        if ($request->hasFile('photo')) {
            $employee = Employee::create($validated);
            if ($employee->storePhoto($request->file('photo'))) {
                return redirect()->route('employees.show', $employee)
                    ->with('success', "Employee created successfully with barcode: {$employee->EMP_BioID}");
            } else {
                return redirect()->back()
                    ->withErrors(['photo' => 'Failed to upload photo'])
                    ->withInput();
            }
        }

        $employee = Employee::create($validated);

        return redirect()->route('employees.show', $employee)
            ->with('success', "Employee created successfully with barcode: {$employee->EMP_BioID}");
    }

    /**
     * Display the specified employee
     */
    public function show(Employee $employee)
    {
        $employee->load(['attendanceLogs' => function ($query) {
            $query->latest('logged_at')->take(10);
        }]);

        return Inertia::render('Employees/Show', [
            'employee' => $employee,
            'recentLogs' => $employee->attendanceLogs,
        ]);
    }

    /**
     * Show the form for editing the specified employee
     */
    public function edit(Employee $employee)
    {
        return Inertia::render('Employees/Edit', [
            'employee' => $employee,
        ]);
    }

    /**
     * Update the specified employee
     */
    public function update(UpdateEmployeeRequest $request, Employee $employee)
    {
        $validated = $request->validated();

        // Handle photo deletion
        if ($request->boolean('delete_photo')) {
            $employee->deletePhoto();
        }

        // Handle photo upload
        if ($request->hasFile('photo')) {
            if (!$employee->storePhoto($request->file('photo'))) {
                return redirect()->back()
                    ->withErrors(['photo' => 'Failed to upload photo'])
                    ->withInput();
            }
        }

        $employee->update($validated);

        return redirect()->route('employees.show', $employee)
            ->with('success', 'Employee updated successfully.');
    }

    /**
     * Remove the specified employee
     */
    public function destroy(Employee $employee)
    {
        $employee->delete();

        return redirect()->route('employees.index')
                        ->with('success', 'Employee deleted successfully.');
    }
}
