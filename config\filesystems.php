<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application for file storage.
    |
    */

    'default' => env('FILESYSTEM_DISK', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Below you may configure as many filesystem disks as necessary, and you
    | may even configure multiple disks for the same driver. Examples for
    | most supported storage drivers are configured here for reference.
    |
    | Supported drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app/private'),
            'serve' => true,
            'throw' => false,
            'report' => false,
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
            'throw' => false,
            'report' => false,
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
            'report' => false,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Employee Photo Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for employee photo storage and URL generation.
    | This allows flexible photo path handling supporting different storage
    | drivers, custom folder structures, external URLs, and configurable
    | photo URL generation.
    |
    */

    'employee_photos' => [
        // Storage disk to use for employee photos
        'disk' => env('EMPLOYEE_PHOTO_DISK', 'public'),

        // Directory within the storage disk (leave empty for root)
        'directory' => env('EMPLOYEE_PHOTO_DIRECTORY', ''),

        // Custom base URL for photos (useful for CDN)
        'base_url' => env('EMPLOYEE_PHOTO_BASE_URL'),

        // URL prefix for public disk (when not using custom base URL)
        'url_prefix' => env('EMPLOYEE_PHOTO_URL_PREFIX', '/storage'),

        // Custom storage path (leave empty for auto-detection)
        'storage_path' => env('EMPLOYEE_PHOTO_STORAGE_PATH'),

        // Fallback service for placeholder images
        // Options: ui-avatars, gravatar, local, none, or custom URL
        'fallback' => env('EMPLOYEE_PHOTO_FALLBACK', 'ui-avatars'),

        // Placeholder image settings
        'placeholder' => [
            'size' => env('EMPLOYEE_PHOTO_PLACEHOLDER_SIZE', 200),
            'background' => env('EMPLOYEE_PHOTO_PLACEHOLDER_BG', '6366f1'),
            'color' => env('EMPLOYEE_PHOTO_PLACEHOLDER_COLOR', 'ffffff'),
            'local_path' => env('EMPLOYEE_PHOTO_LOCAL_PLACEHOLDER', '/images/default-avatar.png'),
        ],

        // Photo validation settings
        'validation' => [
            'max_size' => env('EMPLOYEE_PHOTO_MAX_SIZE', 5120), // KB
            'min_width' => env('EMPLOYEE_PHOTO_MIN_WIDTH', 100),
            'min_height' => env('EMPLOYEE_PHOTO_MIN_HEIGHT', 100),
            'max_width' => env('EMPLOYEE_PHOTO_MAX_WIDTH', 2000),
            'max_height' => env('EMPLOYEE_PHOTO_MAX_HEIGHT', 2000),
        ],

        // Security: Allowed absolute paths (for database photos with full paths)
        'allowed_paths' => env('EMPLOYEE_PHOTO_ALLOWED_PATHS') ?
            explode(',', env('EMPLOYEE_PHOTO_ALLOWED_PATHS')) : [],
    ],

];
