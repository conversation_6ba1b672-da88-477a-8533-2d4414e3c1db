<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class EmployeePhotoController extends Controller
{
    /**
     * Serve employee photo by employee ID
     */
    public function show(Employee $employee): Response|BinaryFileResponse
    {
        if (empty($employee->EMP_PhotoPath)) {
            return $this->notFound();
        }

        $photoPath = $employee->EMP_PhotoPath;

        // Handle absolute file paths
        if ($this->isAbsolutePath($photoPath)) {
            return $this->serveAbsolutePath($photoPath);
        }

        // Handle storage paths
        if (strpos($photoPath, 'storage\\app\\public\\') === 0 || 
            strpos($photoPath, 'storage/app/public/') === 0) {
            $filename = str_replace(['storage\\app\\public\\', 'storage/app/public/'], '', $photoPath);
            return $this->serveFromStorage($filename);
        }

        // Handle relative paths
        return $this->serveFromStorage(ltrim($photoPath, '/'));
    }

    /**
     * Check if a path is absolute
     */
    private function isAbsolutePath(string $path): bool
    {
        // Windows absolute paths (C:\, D:\, etc.)
        if (preg_match('/^[A-Za-z]:[\\\\\/]/', $path)) {
            return true;
        }
        
        // Unix absolute paths (starting with /)
        if (strpos($path, '/') === 0) {
            return true;
        }
        
        return false;
    }

    /**
     * Serve photo from absolute file path with security checks
     */
    private function serveAbsolutePath(string $path): Response|BinaryFileResponse
    {
        $config = config('filesystems.employee_photos');
        $allowedPaths = $config['allowed_paths'] ?? [];
        
        // Security check: ensure path is in allowed directories
        if (!empty($allowedPaths)) {
            $isAllowed = false;
            foreach ($allowedPaths as $allowedPath) {
                $allowedPath = trim($allowedPath);
                if (strpos($path, $allowedPath) === 0) {
                    $isAllowed = true;
                    break;
                }
            }
            
            if (!$isAllowed) {
                \Log::warning("Employee photo path not in allowed directories: {$path}");
                return $this->notFound();
            }
        }

        // Check if file exists
        if (!file_exists($path)) {
            return $this->notFound();
        }

        // Validate file type for security
        $mimeType = mime_content_type($path);
        $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        
        if (!in_array($mimeType, $allowedMimes)) {
            \Log::warning("Invalid file type for employee photo: {$mimeType}");
            return $this->notFound();
        }

        // Serve the file
        return response()->file($path, [
            'Content-Type' => $mimeType,
            'Cache-Control' => 'public, max-age=3600', // Cache for 1 hour
        ]);
    }

    /**
     * Serve photo from configured storage
     */
    private function serveFromStorage(string $filename): Response|BinaryFileResponse
    {
        $config = config('filesystems.employee_photos');
        $disk = $config['disk'] ?? 'public';
        $directory = $config['directory'] ?? '';
        
        try {
            $storage = Storage::disk($disk);
            $filePath = $directory ? $directory . '/' . $filename : $filename;
            
            // Try case-insensitive file matching
            $actualFile = $this->findFileWithCaseInsensitive($storage, $filePath);
            
            if ($actualFile && $storage->exists($actualFile)) {
                $fullPath = $storage->path($actualFile);
                $mimeType = $storage->mimeType($actualFile);
                
                return response()->file($fullPath, [
                    'Content-Type' => $mimeType,
                    'Cache-Control' => 'public, max-age=3600',
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to serve photo from storage: ' . $e->getMessage());
        }
        
        return $this->notFound();
    }

    /**
     * Find file with case-insensitive matching
     */
    private function findFileWithCaseInsensitive($storage, string $filePath): ?string
    {
        // First try exact match
        if ($storage->exists($filePath)) {
            return $filePath;
        }
        
        // Try case-insensitive match
        $directory = dirname($filePath);
        $filename = basename($filePath);
        
        if ($directory === '.') {
            $directory = '';
        }
        
        try {
            $files = $storage->files($directory);
            foreach ($files as $file) {
                if (strtolower(basename($file)) === strtolower($filename)) {
                    return $file;
                }
            }
        } catch (\Exception $e) {
            // If listing files fails, return null
        }
        
        return null;
    }

    /**
     * Return 404 response for missing photos
     */
    private function notFound(): Response
    {
        return response('Photo not found', 404);
    }
}
