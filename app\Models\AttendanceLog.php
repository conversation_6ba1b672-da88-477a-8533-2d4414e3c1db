<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class AttendanceLog extends Model
{
    protected $table = 'attendance_logs';
    public $timestamps = false; 
    public $incrementing = true; // Enable auto-incrementing since database ID column is now identity
    protected $keyType = 'int'; // Set key type as integer

    protected $fillable = [
        'EMP_EmpNo',
        'EMP_BioID',
        'action_type',
        'logged_at',
        'log_date',
        'log_time',
    ];

    protected $casts = [
        'logged_at' => 'datetime',
    ];

    /**
     * Boot method to auto-populate date and time fields using Clarion format
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($log) {
            // Use current timestamp if logged_at is not provided
            if (empty($log->logged_at)) {
                $log->logged_at = now();
            }

            // Parse the logged_at timestamp to ensure all time fields are synchronized
            $loggedAt = Carbon::parse($log->logged_at);

            // Set log_date in Clarion format (days since December 28, 1800)
            $log->log_date = static::toClarionDate($loggedAt);

            // Set log_time in Clarion format (centiseconds since midnight)
            $log->log_time = static::toClarionTime($loggedAt);
        });
    }

    /**
     * Convert a Carbon date to Clarion date format (days since December 28, 1800)
     */
    public static function toClarionDate(Carbon $date): int
    {
        $clarionEpoch = Carbon::create(1800, 12, 28, 0, 0, 0);
        return $clarionEpoch->diffInDays($date, false); // false = signed difference
    }

    /**
     * Convert a Carbon time to Clarion time format (centiseconds since midnight)
     */
    public static function toClarionTime(Carbon $time): int
    {
        $midnight = $time->copy()->startOfDay();
        return $midnight->diffInSeconds($time, false) * 100; // Convert to centiseconds, false = signed
    }

    /**
     * Convert Clarion date format back to Carbon date
     */
    public static function fromClarionDate(int $clarionDate): Carbon
    {
        $clarionEpoch = Carbon::create(1800, 12, 28, 0, 0, 0);
        return $clarionEpoch->addDays($clarionDate);
    }

    /**
     * Convert Clarion time format back to time string
     */
    public static function fromClarionTime(int $clarionTime): string
    {
        $totalSeconds = intval($clarionTime / 100); // Convert from centiseconds
        $hours = intval($totalSeconds / 3600);
        $minutes = intval(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Get the employee that owns this log
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'EMP_EmpNo', 'EMP_EmpNo');
    }



    /**
     * Scope for today's logs
     */
    public function scopeToday($query)
    {
        $clarionToday = static::toClarionDate(today());
        return $query->where('log_date', $clarionToday);
    }

    /**
     * Scope for specific employee
     */
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('EMP_EmpNo', $employeeId);
    }

    /**
     * Scope for specific action type
     */
    public function scopeOfType($query, $actionType)
    {
        return $query->where('action_type', $actionType);
    }

    /**
     * Scope for specific action type (by string)
     */
    public function scopeOfTypeString($query, $actionTypeString)
    {
        $actionTypeMap = [
            'check_in' => 1,
            'check_out' => 2,
            'break_in' => 3,
            'break_out' => 4,
        ];

        $actionTypeId = $actionTypeMap[$actionTypeString] ?? null;

        if ($actionTypeId) {
            return $query->where('action_type', $actionTypeId);
        }

        return $query->whereRaw('1 = 0'); // Return no results if action type not found
    }

    /**
     * Get formatted time for display
     */
    public function getFormattedTimeAttribute(): string
    {
        return $this->logged_at->format('g:i A');
    }

    /**
     * Get formatted date for display
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->logged_at->format('M d, Y');
    }

    /**
     * Get action type label (using numeric system)
     */
    public function getActionLabelAttribute(): string
    {
        $actionLabels = [
            1 => 'Check In',
            2 => 'Check Out',
            3 => 'Break In',
            4 => 'Break Out',
        ];

        return $actionLabels[$this->action_type] ?? 'Unknown Action';
    }

    /**
     * Get action type string
     */
    public function getActionTypeStringAttribute(): string
    {
        $actionStrings = [
            1 => 'check_in',
            2 => 'check_out',
            3 => 'break_in',
            4 => 'break_out',
        ];

        return $actionStrings[$this->action_type] ?? 'unknown';
    }
}
