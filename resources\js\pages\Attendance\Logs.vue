<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-7xl mx-auto">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-800">Time Tracking & Hours</h1>
          <p class="text-gray-600 mt-1"></p>
        </div>
        <div class="space-x-3">
          <Button
            label="Export Excel"
            icon="pi pi-download"
            @click="exportToExcel"
            class="p-button-success"
          />
          <Button
            label="Back to Scanner"
            icon="pi pi-arrow-left"
            @click="$inertia.visit(route('attendance.index'))"
            class="p-button-secondary"
          />
          <Button
            label="Employees"
            icon="pi pi-users"
            @click="$inertia.visit(route('employees.index'))"
            class="p-button-info"
          />
        </div>
      </div>

      <!-- Search Bar -->
   

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4 text-gray-700">Filter Logs</h2>
        <div class="grid md:grid-cols-5 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Date From</label>
            <Calendar
              v-model="filterForm.date_from"
              date-format="yy-mm-dd"
              class="w-full"
              @date-select="applyFilters"
              placeholder="Select start date"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Date To</label>
            <Calendar
              v-model="filterForm.date_to"
              date-format="yy-mm-dd"
              class="w-full"
              @date-select="applyFilters"
              placeholder="Select end date"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Employees</label>
            <MultiSelect
              v-model="filterForm.employees"
              :options="employeeOptions"
              option-label="label"
              option-value="value"
              placeholder="Select employees"
              class="w-full"
              @change="applyFilters"
              :max-selected-labels="2"
              selected-items-label="{0} employees selected"
            >
              <template #option="slotProps">
                <div class="flex items-center gap-2">
                  <img
                    v-if="slotProps.option.photo"
                    :src="getPhotoUrl(slotProps.option.photo)"
                    :alt="slotProps.option.label"
                    class="w-8 h-8 rounded-full object-cover"
                    @error="handleImageError($event, slotProps.option)"
                  />
                  <div
                    v-else
                    class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium"
                  >
                    {{ getInitials(slotProps.option.label) }}
                  </div>
                  <span>{{ slotProps.option.label }}</span>
                </div>
              </template>
            </MultiSelect>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Action Type</label>
            <Select
              v-model="filterForm.action_type"
              :options="actionOptions"
              option-label="label"
              option-value="value"
              placeholder="All Actions"
              class="w-full"
              @change="applyFilters"
            />
          </div>
          <div class="flex items-end">
            <Button 
              label="Clear Filters" 
              icon="pi pi-times" 
              @click="clearFilters"
              class="p-button-text"
            />
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-800">Total Hours by Employee</h2>
          <p class="text-sm text-gray-600 mt-1">
            Consolidated hours worked per employee
            <span v-if="dateRangeText" class="font-medium text-blue-600">{{ dateRangeText }}</span>
          </p>
        </div>
        <DataTable
          :value="consolidatedHours"
          :paginator="true"
          :rows="25"
          paginator-template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
          current-page-report-template="Showing {first} to {last} of {totalRecords} entries"
          :rows-per-page-options="[10, 25, 50]"
          table-style="min-width: 50rem"
          class="p-datatable-sm"
          :sort-field="'total_hours'"
          :sort-order="-1"
        >
          <Column field="employee.EMP_FullName" header="Employee" sortable style="min-width: 250px">
            <template #body="slotProps">
              <div class="flex items-center gap-3">
                <img
                  v-if="slotProps.data.employee?.EMP_PhotoPath"
                  :src="getPhotoUrl(slotProps.data.employee.EMP_PhotoPath)"
                  :alt="slotProps.data.employee.EMP_FullName"
                  class="w-12 h-12 rounded-full object-cover border border-gray-200"
                  @error="handleImageError($event, slotProps.data.employee)"
                />
                <div
                  v-else
                  class="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium"
                >
                  {{ getInitials(slotProps.data.employee?.EMP_FullName || 'Unknown') }}
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ slotProps.data.employee?.EMP_FullName || 'Unknown' }}
                  </div>
                  <div class="text-xs text-gray-500">ID: {{ slotProps.data.employee?.EMP_EmpNo }}</div>
                </div>
              </div>
            </template>
          </Column>

          <Column field="total_hours" header="Total Hours Worked" sortable>
            <template #body="slotProps">
              <div class="text-lg font-bold text-blue-600">
                {{ formatHours(slotProps.data.total_hours) }}
              </div>
            </template>
          </Column>

          <!-- <Column field="session_count" header="Total Sessions" sortable>
            <template #body="slotProps">
              <div class="text-sm text-gray-700">
                {{ slotProps.data.session_count }} sessions
              </div>
            </template>
          </Column> -->

          <Column field="working_days" header="Working Days" sortable>
            <template #body="slotProps">
              <div class="text-sm text-gray-700">
                {{ slotProps.data.working_days }} days
              </div>
            </template>
          </Column>

          <Column field="average_hours_per_day" header="Avg Hours/Day" sortable>
            <template #body="slotProps">
              <div class="text-sm text-gray-600">
                {{ formatHours(slotProps.data.average_hours_per_day) }}
              </div>
            </template>
          </Column>
        </DataTable>
      </div>

      <!-- Empty State -->
      <div v-if="consolidatedHours.length === 0" class="text-center py-12">
        <i class="pi pi-clock text-6xl text-gray-400 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No attendance data found</h3>
        <p class="text-gray-600 mb-4">Try adjusting your date range or employee filters to see consolidated hours data.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import Button from 'primevue/button'
import Calendar from 'primevue/calendar'
import Select from 'primevue/select'
import MultiSelect from 'primevue/multiselect'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'

const props = defineProps({
  logs: Object,
  mergedSessions: Array,
  employees: Array,
  actionTypes: Array,
  filters: Object,
})

const filterForm = reactive({
  date_from: props.filters.date_from ? new Date(props.filters.date_from) : null,
  date_to: props.filters.date_to ? new Date(props.filters.date_to) : null,
  employees: props.filters.employees || [],
  action_type: props.filters.action_type || '',
})

const employeeOptions = computed(() =>
  props.employees.map(emp => ({
    label: emp.EMP_FullName,
    value: emp.EMP_EmpNo,
    photo: emp.EMP_PhotoPath
  }))
)





const actionOptions = computed(() => [
  { label: 'All Actions', value: '' },
  ...props.actionTypes.map(actionType => ({
    label: actionType.action_label,
    value: actionType.action_type
  }))
])

// Date range text for display
const dateRangeText = computed(() => {
  if (filterForm.date_from && filterForm.date_to) {
    const fromDate = formatDateForDisplay(filterForm.date_from)
    const toDate = formatDateForDisplay(filterForm.date_to)
    return `(${fromDate} to ${toDate})`
  } else if (filterForm.date_from) {
    return `(from ${formatDateForDisplay(filterForm.date_from)})`
  } else if (filterForm.date_to) {
    return `(until ${formatDateForDisplay(filterForm.date_to)})`
  }
  return '(last 7 days)'
})

// Consolidated hours per employee for the filtered date range
const consolidatedHours = computed(() => {
  if (!props.mergedSessions || props.mergedSessions.length === 0) {
    return []
  }

  const employeeData = {}

  props.mergedSessions.forEach(session => {
    const empNo = session.employee?.EMP_EmpNo
    if (!empNo) return

    if (!employeeData[empNo]) {
      employeeData[empNo] = {
        employee: session.employee,
        total_hours: 0,
        session_count: 0,
        working_days: new Set(),
        sessions: []
      }
    }

    const empData = employeeData[empNo]
    empData.sessions.push(session)
    empData.session_count++

    // Only count complete sessions for hours
    if (session.is_complete && session.duration_hours > 0) {
      empData.total_hours += session.duration_hours
    }

    // Track unique working days
    if (session.date) {
      empData.working_days.add(session.date)
    }
  })

  // Convert to array and calculate averages
  return Object.values(employeeData).map(empData => ({
    employee: empData.employee,
    total_hours: empData.total_hours,
    session_count: empData.session_count,
    working_days: empData.working_days.size,
    average_hours_per_day: empData.working_days.size > 0
      ? empData.total_hours / empData.working_days.size
      : 0,
    sessions: empData.sessions
  })).sort((a, b) => b.total_hours - a.total_hours) // Sort by total hours descending
})

function applyFilters() {
  sanitizeFilters()
  
  const params = {
    date_from: filterForm.date_from ? formatDateForBackend(filterForm.date_from) : '',
    date_to: filterForm.date_to ? formatDateForBackend(filterForm.date_to) : '',
    employees: filterForm.employees,
    action_type: filterForm.action_type,
  }
  
  router.get(route('attendance.logs'), params, {
    preserveState: true,
    preserveScroll: true,
  })
}

function clearFilters() {
  filterForm.date_from = null
  filterForm.date_to = null
  filterForm.employees = []
  filterForm.action_type = ''
  applyFilters()
}

function sanitizeFilters() {
  // Ensure employees array contains only numeric values
  if (Array.isArray(filterForm.employees)) {
    filterForm.employees = filterForm.employees.filter(emp => 
      emp && !isNaN(Number(emp))
    )
  }
  
  // Ensure action_type is either empty or numeric
  if (filterForm.action_type && !Number.isInteger(Number(filterForm.action_type))) {
    filterForm.action_type = ''
  }
}

function formatDateForBackend(date) {
  if (!date) return ''
  return date.toISOString().split('T')[0]
}

function formatDateForDisplay(date) {
  if (!date) return ''
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

function exportToExcel() {
  const params = new URLSearchParams({
    date_from: filterForm.date_from ? formatDateForBackend(filterForm.date_from) : '',
    date_to: filterForm.date_to ? formatDateForBackend(filterForm.date_to) : '',
    employees: JSON.stringify(filterForm.employees),
    action_type: filterForm.action_type,
  })
  
  window.open(`${route('attendance.logs.export')}?${params.toString()}`, '_blank')
}

function formatHours(hours) {
  if (!hours || hours === 0) return '0h 0m'

  const wholeHours = Math.floor(hours)
  const minutes = Math.round((hours - wholeHours) * 60)

  return `${wholeHours}h ${minutes}m`
}

function getInitials(name) {
  if (!name) return '?'
  return name.split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

function getPhotoUrl(photoPath) {
  if (!photoPath) return null

  // If path already starts with storage/, use as is
  if (photoPath.startsWith('storage/')) {
    return `/${photoPath}`
  }

  // If path contains storage\app\public, extract the relevant part
  if (photoPath.includes('storage\\app\\public\\')) {
    const filename = photoPath.split('storage\\app\\public\\')[1]
    return `/storage/${filename}`
  }

  // Default: assume it's just a filename
  return `/storage/${photoPath}`
}

function handleImageError(event, employee) {
  // Hide broken image and show initials fallback
  event.target.style.display = 'none'
  console.log('Image failed to load for employee:', employee?.EMP_FullName || employee?.label)
}


</script>
