<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Record Attendance Request
 * 
 * Handles validation for recording attendance actions with comprehensive
 * validation rules and custom error messages.
 * 
 * @package App\Http\Requests
 */
class RecordAttendanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_code' => [
                'required',
                'string',
                'min:6',
                'max:20',
                'regex:/^EMP\d{5}$/', // Must match EMP + 5 digits format
            ],
            'Emp_Type' => [
                'required',
                'string',
                Rule::in(['check_in', 'check_out', 'break_in', 'break_out']),
            ],
            'notes' => [
                'nullable',
                'string',
                'max:500',
            ],
            'location' => [
                'nullable',
                'string',
                'max:100',
            ],
            'device_info' => [
                'nullable',
                'array',
            ],
            'device_info.type' => [
                'nullable',
                'string',
                'max:50',
            ],
            'device_info.identifier' => [
                'nullable',
                'string',
                'max:100',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'employee_code.required' => 'Employee code is required.',
            'employee_code.min' => 'Employee code must be at least 6 characters.',
            'employee_code.max' => 'Employee code must not exceed 20 characters.',
            'employee_code.regex' => 'Employee code must be in format EMP followed by 5 digits (e.g., EMP12345).',
            'Emp_Type.required' => 'Action type is required.',
            'Emp_Type.in' => 'Action type must be one of: check_in, check_out, break_in, break_out.',
            'notes.max' => 'Notes must not exceed 500 characters.',
            'location.max' => 'Location must not exceed 100 characters.',
            'device_info.array' => 'Device info must be a valid array.',
            'device_info.type.max' => 'Device type must not exceed 50 characters.',
            'device_info.identifier.max' => 'Device identifier must not exceed 100 characters.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'employee_code' => 'employee code',
            'Emp_Type' => 'action type',
            'notes' => 'notes',
            'location' => 'location',
            'device_info.type' => 'device type',
            'device_info.identifier' => 'device identifier',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Normalize employee code to uppercase
        if ($this->filled('employee_code')) {
            $this->merge(['employee_code' => strtoupper($this->employee_code)]);
        }

        // Normalize action type to lowercase
        if ($this->filled('Emp_Type')) {
            $this->merge(['Emp_Type' => strtolower($this->Emp_Type)]);
        }

        // Trim notes if provided
        if ($this->filled('notes')) {
            $this->merge(['notes' => trim($this->notes)]);
        }

        // Trim location if provided
        if ($this->filled('location')) {
            $this->merge(['location' => trim($this->location)]);
        }
    }

    /**
     * Get the validated data with additional processing.
     */
    public function getValidatedData(): array
    {
        $validated = $this->validated();
        
        // Add timestamp
        $validated['timestamp'] = now();
        
        // Add IP address
        $validated['ip_address'] = $this->ip();
        
        // Add user agent
        $validated['user_agent'] = $this->userAgent();
        
        return $validated;
    }

    /**
     * Handle a passed validation attempt.
     */
    protected function passedValidation(): void
    {
        // Log successful validation
        \Log::info('Attendance request validated successfully', [
            'employee_code' => $this->employee_code,
            'action_type' => $this->Emp_Type,
            'ip_address' => $this->ip(),
        ]);
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        // Log validation failure
        \Log::warning('Attendance request validation failed', [
            'employee_code' => $this->employee_code ?? 'not provided',
            'action_type' => $this->Emp_Type ?? 'not provided',
            'errors' => $validator->errors()->toArray(),
            'ip_address' => $this->ip(),
        ]);

        parent::failedValidation($validator);
    }
}
