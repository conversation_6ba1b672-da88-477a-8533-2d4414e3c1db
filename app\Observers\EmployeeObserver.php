<?php

namespace App\Observers;

use App\Models\Employee;
use Illuminate\Support\Facades\Log;

/**
 * Employee Observer
 * 
 * Handles model events for Employee model to maintain data consistency,
 * perform logging, and execute business logic when employees are created,
 * updated, or deleted.
 * 
 * @package App\Observers
 */
class EmployeeObserver
{
    /**
     * Handle the Employee "creating" event.
     * 
     * This runs before a new employee is saved to the database.
     */
    public function creating(Employee $employee): void
    {
        // Auto-generate BioID if not provided
        if (empty($employee->EMP_BioID)) {
            $employee->EMP_BioID = Employee::generateBioID();
        }

        // Auto-generate full name if not provided
        if (empty($employee->EMP_FullName)) {
            $employee->EMP_FullName = trim(
                ($employee->EMP_FirstName ?? '') . ' ' .
                ($employee->EMP_MiddleName ?? '') . ' ' .
                ($employee->EMP_LastName ?? '')
            );
        }

        // Ensure employee number is uppercase
        if (!empty($employee->EMP_EmpNo)) {
            $employee->EMP_EmpNo = strtoupper($employee->EMP_EmpNo);
        }

        // Set default values
        if (is_null($employee->EMP_IsActive)) {
            $employee->EMP_IsActive = 1; // Default to active
        }

        // Log the creation attempt
        Log::info('Creating new employee', [
            'emp_no' => $employee->EMP_EmpNo,
            'bio_id' => $employee->EMP_BioID,
            'full_name' => $employee->EMP_FullName,
            'department' => $employee->EMP_Department,
        ]);
    }

    /**
     * Handle the Employee "created" event.
     * 
     * This runs after a new employee has been saved to the database.
     */
    public function created(Employee $employee): void
    {
        // Log successful creation
        Log::info('Employee created successfully', [
            'emp_id' => $employee->EMP_EmpID,
            'emp_no' => $employee->EMP_EmpNo,
            'bio_id' => $employee->EMP_BioID,
            'full_name' => $employee->EMP_FullName,
        ]);

        // Here you could trigger additional actions like:
        // - Sending welcome email
        // - Creating default permissions
        // - Notifying HR department
        // - Generating employee badge
    }

    /**
     * Handle the Employee "updating" event.
     * 
     * This runs before an employee is updated in the database.
     */
    public function updating(Employee $employee): void
    {
        // Update full name if name fields changed
        if ($employee->isDirty(['EMP_FirstName', 'EMP_LastName', 'EMP_MiddleName'])) {
            $employee->EMP_FullName = trim(
                ($employee->EMP_FirstName ?? '') . ' ' .
                ($employee->EMP_MiddleName ?? '') . ' ' .
                ($employee->EMP_LastName ?? '')
            );
        }

        // Ensure employee number is uppercase
        if ($employee->isDirty('EMP_EmpNo') && !empty($employee->EMP_EmpNo)) {
            $employee->EMP_EmpNo = strtoupper($employee->EMP_EmpNo);
        }

        // Log significant changes
        $changes = $employee->getDirty();
        if (!empty($changes)) {
            Log::info('Updating employee', [
                'emp_id' => $employee->EMP_EmpID,
                'emp_no' => $employee->EMP_EmpNo,
                'changes' => array_keys($changes),
            ]);

            // Log status changes specifically
            if (isset($changes['EMP_IsActive'])) {
                $oldStatus = $employee->getOriginal('EMP_IsActive') ? 'active' : 'inactive';
                $newStatus = $changes['EMP_IsActive'] ? 'active' : 'inactive';
                
                Log::warning('Employee status changed', [
                    'emp_id' => $employee->EMP_EmpID,
                    'emp_no' => $employee->EMP_EmpNo,
                    'full_name' => $employee->EMP_FullName,
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                ]);
            }
        }
    }

    /**
     * Handle the Employee "updated" event.
     * 
     * This runs after an employee has been updated in the database.
     */
    public function updated(Employee $employee): void
    {
        // Log successful update
        Log::info('Employee updated successfully', [
            'emp_id' => $employee->EMP_EmpID,
            'emp_no' => $employee->EMP_EmpNo,
            'full_name' => $employee->EMP_FullName,
        ]);

        // Handle status change notifications
        if ($employee->wasChanged('EMP_IsActive')) {
            $this->handleStatusChange($employee);
        }

        // Handle department change notifications
        if ($employee->wasChanged('EMP_Department')) {
            $this->handleDepartmentChange($employee);
        }
    }

    /**
     * Handle the Employee "deleting" event.
     * 
     * This runs before an employee is deleted from the database.
     */
    public function deleting(Employee $employee): void
    {
        Log::warning('Deleting employee', [
            'emp_id' => $employee->EMP_EmpID,
            'emp_no' => $employee->EMP_EmpNo,
            'full_name' => $employee->EMP_FullName,
            'department' => $employee->EMP_Department,
        ]);

        // Here you might want to:
        // - Archive attendance logs
        // - Clean up related data
        // - Send notifications
        // - Prevent deletion if there are recent attendance logs
    }

    /**
     * Handle the Employee "deleted" event.
     * 
     * This runs after an employee has been deleted from the database.
     */
    public function deleted(Employee $employee): void
    {
        Log::warning('Employee deleted', [
            'emp_id' => $employee->EMP_EmpID,
            'emp_no' => $employee->EMP_EmpNo,
            'full_name' => $employee->EMP_FullName,
        ]);

        // Clean up photo file if it exists
        if (!empty($employee->EMP_PhotoPath)) {
            try {
                $photoPath = storage_path('app/public/' . basename($employee->EMP_PhotoPath));
                if (file_exists($photoPath)) {
                    unlink($photoPath);
                    Log::info('Employee photo deleted', ['photo_path' => $photoPath]);
                }
            } catch (\Exception $e) {
                Log::error('Failed to delete employee photo', [
                    'photo_path' => $employee->EMP_PhotoPath,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Handle employee status changes
     */
    private function handleStatusChange(Employee $employee): void
    {
        $isActive = $employee->EMP_IsActive;
        $status = $isActive ? 'activated' : 'deactivated';
        
        Log::info("Employee {$status}", [
            'emp_id' => $employee->EMP_EmpID,
            'emp_no' => $employee->EMP_EmpNo,
            'full_name' => $employee->EMP_FullName,
            'new_status' => $status,
        ]);

        // Here you could:
        // - Send notification to HR
        // - Update access permissions
        // - Send email to employee
        // - Update related systems
    }

    /**
     * Handle employee department changes
     */
    private function handleDepartmentChange(Employee $employee): void
    {
        $oldDepartment = $employee->getOriginal('EMP_Department');
        $newDepartment = $employee->EMP_Department;
        
        Log::info('Employee department changed', [
            'emp_id' => $employee->EMP_EmpID,
            'emp_no' => $employee->EMP_EmpNo,
            'full_name' => $employee->EMP_FullName,
            'old_department' => $oldDepartment,
            'new_department' => $newDepartment,
        ]);

        // Here you could:
        // - Notify old and new department managers
        // - Update reporting structures
        // - Transfer access permissions
        // - Update organizational charts
    }
}
