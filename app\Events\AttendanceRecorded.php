<?php

namespace App\Events;

use App\Models\AttendanceLog;
use App\Models\Employee;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Attendance Recorded Event
 * 
 * Fired when an attendance action is successfully recorded.
 * This event can trigger various background processes like
 * notifications, reports, or integrations.
 * 
 * @package App\Events
 */
class AttendanceRecorded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public AttendanceLog $attendanceLog;
    public Employee $employee;
    public string $actionLabel;
    public array $metadata;

    /**
     * Create a new event instance.
     */
    public function __construct(
        AttendanceLog $attendanceLog,
        Employee $employee,
        string $actionLabel,
        array $metadata = []
    ) {
        $this->attendanceLog = $attendanceLog;
        $this->employee = $employee;
        $this->actionLabel = $actionLabel;
        $this->metadata = $metadata;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('attendance'),
            new PrivateChannel('employee.' . $this->employee->EMP_EmpNo),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'attendance_log' => [
                'id' => $this->attendanceLog->id,
                'action_type' => $this->attendanceLog->action_type,
                'action_label' => $this->actionLabel,
                'logged_at' => $this->attendanceLog->logged_at->toISOString(),
                'formatted_time' => $this->attendanceLog->logged_at->format('g:i A'),
            ],
            'employee' => [
                'emp_no' => $this->employee->EMP_EmpNo,
                'bio_id' => $this->employee->EMP_BioID,
                'full_name' => $this->employee->EMP_FullName,
                'department' => $this->employee->EMP_Department,
                'photo_url' => $this->employee->photo_url,
            ],
            'metadata' => $this->metadata,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'attendance.recorded';
    }
}
