# Employee Photo Configuration Examples
# Add these to your .env file to customize employee photo handling

# Storage Disk (public, local, s3, or custom disk name)
EMPLOYEE_PHOTO_DISK=public

# Directory within the storage disk (leave empty for root)
EMPLOYEE_PHOTO_DIRECTORY=employees

# Custom base URL for photos (useful for CDN)
# EMPLOYEE_PHOTO_BASE_URL=https://cdn.example.com/employee-photos

# URL prefix for public disk
EMPLOYEE_PHOTO_URL_PREFIX=/storage

# Custom storage path (leave empty for auto-detection)
# EMPLOYEE_PHOTO_STORAGE_PATH=/var/www/storage/app/public

# Fallback service for placeholder images
# Options: ui-avatars, gravatar, local, none, or custom URL
EMPLOYEE_PHOTO_FALLBACK=ui-avatars

# Placeholder image settings
EMPLOYEE_PHOTO_PLACEHOLDER_SIZE=200
EMPLOYEE_PHOTO_PLACEHOLDER_BG=6366f1
EMPLOYEE_PHOTO_PLACEHOLDER_COLOR=ffffff

# Local placeholder image path (when using 'local' fallback)
EMPLOYEE_PHOTO_LOCAL_PLACEHOLDER=/images/default-avatar.png

# Photo validation settings
EMPLOYEE_PHOTO_MAX_SIZE=5120
EMPLOYEE_PHOTO_MIN_WIDTH=100
EMPLOYEE_PHOTO_MIN_HEIGHT=100
EMPLOYEE_PHOTO_MAX_WIDTH=2000
EMPLOYEE_PHOTO_MAX_HEIGHT=2000

# Example configurations for different scenarios:

# 1. Store photos in a custom subdirectory
# EMPLOYEE_PHOTO_DIRECTORY=photos/employees

# 2. Use S3 storage
# EMPLOYEE_PHOTO_DISK=s3
# EMPLOYEE_PHOTO_DIRECTORY=employee-photos

# 3. Use custom CDN URL
# EMPLOYEE_PHOTO_BASE_URL=https://mycdn.example.com/photos

# 4. Use Gravatar with UI Avatars fallback
# EMPLOYEE_PHOTO_FALLBACK=gravatar

# 5. Use local placeholder image
# EMPLOYEE_PHOTO_FALLBACK=local
# EMPLOYEE_PHOTO_LOCAL_PLACEHOLDER=/images/employee-placeholder.png

# 6. Disable placeholder images
# EMPLOYEE_PHOTO_FALLBACK=none

# Security Configuration for Absolute Paths
# ==========================================

# When employees have photos stored as absolute file paths (e.g., C:\Users\<USER>\Desktop\photo.jpg),
# you can configure allowed directories for security:

# Example for Windows paths:
# EMPLOYEE_PHOTO_ALLOWED_PATHS="C:\Users\<USER>\Desktop,C:\EmployeePhotos"

# Example for Unix paths:
# EMPLOYEE_PHOTO_ALLOWED_PATHS="/home/<USER>/photos,/var/employee-photos"

# Leave empty to allow any absolute path (not recommended for production):
# EMPLOYEE_PHOTO_ALLOWED_PATHS=
