<?php

namespace App\Listeners;

use App\Events\AttendanceRecorded;
use App\Jobs\ProcessAttendanceNotification;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * Handle Attendance Recorded Event Listener
 * 
 * Processes attendance recorded events and triggers appropriate
 * background jobs for notifications and business rule processing.
 * 
 * @package App\Listeners
 */
class HandleAttendanceRecorded implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(AttendanceRecorded $event): void
    {
        Log::info('Processing AttendanceRecorded event', [
            'employee' => $event->employee->EMP_FullName,
            'action_type' => $event->attendanceLog->action_type,
            'action_label' => $event->actionLabel,
            'logged_at' => $event->attendanceLog->logged_at->toISOString(),
        ]);

        try {
            // Process different types of notifications based on business rules
            $this->processBusinessRules($event);
            
            // Log successful processing
            Log::info('AttendanceRecorded event processed successfully', [
                'attendance_log_id' => $event->attendanceLog->id,
                'employee' => $event->employee->EMP_FullName,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process AttendanceRecorded event', [
                'attendance_log_id' => $event->attendanceLog->id,
                'employee' => $event->employee->EMP_FullName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }

    /**
     * Process business rules and trigger appropriate notifications
     */
    private function processBusinessRules(AttendanceRecorded $event): void
    {
        $attendanceLog = $event->attendanceLog;
        $employee = $event->employee;
        $actionType = $attendanceLog->action_type;
        $loggedAt = $attendanceLog->logged_at;

        // Check for late arrival (Check In after 9:00 AM)
        if ($actionType === 1) { // Check In
            $this->checkLateArrival($event, $loggedAt);
        }

        // Check for early departure (Check Out before 5:00 PM)
        if ($actionType === 2) { // Check Out
            $this->checkEarlyDeparture($event, $loggedAt);
            $this->checkMissingCheckIn($event);
            $this->checkOvertimeAlert($event);
        }

        // Check for long breaks
        if ($actionType === 4) { // Break Out
            $this->checkLongBreak($event);
        }

        // Check for missing checkout from previous day
        if ($actionType === 1) { // Check In
            $this->checkMissingCheckoutFromPreviousDay($event);
        }
    }

    /**
     * Check for late arrival
     */
    private function checkLateArrival(AttendanceRecorded $event, Carbon $loggedAt): void
    {
        $standardStartTime = $loggedAt->copy()->setTime(9, 0, 0); // 9:00 AM
        
        if ($loggedAt->gt($standardStartTime)) {
            $lateMinutes = $loggedAt->diffInMinutes($standardStartTime);
            
            // Only trigger notification if more than 15 minutes late
            if ($lateMinutes > 15) {
                ProcessAttendanceNotification::dispatch(
                    $event->attendanceLog,
                    $event->employee,
                    'late_arrival',
                    [
                        'late_minutes' => $lateMinutes,
                        'standard_start_time' => $standardStartTime->format('H:i'),
                        'actual_start_time' => $loggedAt->format('H:i'),
                    ]
                );
            }
        }
    }

    /**
     * Check for early departure
     */
    private function checkEarlyDeparture(AttendanceRecorded $event, Carbon $loggedAt): void
    {
        $standardEndTime = $loggedAt->copy()->setTime(17, 0, 0); // 5:00 PM
        
        if ($loggedAt->lt($standardEndTime)) {
            $earlyMinutes = $standardEndTime->diffInMinutes($loggedAt);
            
            // Only trigger notification if more than 30 minutes early
            if ($earlyMinutes > 30) {
                ProcessAttendanceNotification::dispatch(
                    $event->attendanceLog,
                    $event->employee,
                    'early_departure',
                    [
                        'early_minutes' => $earlyMinutes,
                        'standard_end_time' => $standardEndTime->format('H:i'),
                        'actual_end_time' => $loggedAt->format('H:i'),
                    ]
                );
            }
        }
    }

    /**
     * Check for missing check-in (checkout without checkin)
     */
    private function checkMissingCheckIn(AttendanceRecorded $event): void
    {
        $employee = $event->employee;
        $today = $event->attendanceLog->logged_at->startOfDay();
        
        // Get today's logs before this checkout
        $todayLogs = $employee->attendanceLogs()
            ->whereDate('logged_at', $today)
            ->where('id', '<', $event->attendanceLog->id)
            ->orderBy('logged_at')
            ->get();
        
        // Check if there's a check-in before this check-out
        $hasCheckIn = $todayLogs->where('action_type', 1)->isNotEmpty();
        
        if (!$hasCheckIn) {
            ProcessAttendanceNotification::dispatch(
                $event->attendanceLog,
                $event->employee,
                'missing_checkin',
                [
                    'checkout_time' => $event->attendanceLog->logged_at->format('H:i'),
                    'date' => $today->format('Y-m-d'),
                ]
            );
        }
    }

    /**
     * Check for overtime alert
     */
    private function checkOvertimeAlert(AttendanceRecorded $event): void
    {
        $employee = $event->employee;
        $today = $event->attendanceLog->logged_at->startOfDay();
        
        // Get today's check-in
        $checkIn = $employee->attendanceLogs()
            ->whereDate('logged_at', $today)
            ->where('action_type', 1)
            ->orderBy('logged_at')
            ->first();
        
        if ($checkIn) {
            $workHours = $checkIn->logged_at->diffInHours($event->attendanceLog->logged_at);
            
            // Trigger overtime alert if more than 8 hours
            if ($workHours > 8) {
                $overtimeHours = $workHours - 8;
                
                ProcessAttendanceNotification::dispatch(
                    $event->attendanceLog,
                    $event->employee,
                    'overtime_alert',
                    [
                        'total_hours' => $workHours,
                        'overtime_hours' => $overtimeHours,
                        'check_in_time' => $checkIn->logged_at->format('H:i'),
                        'check_out_time' => $event->attendanceLog->logged_at->format('H:i'),
                    ]
                );
            }
        }
    }

    /**
     * Check for long break
     */
    private function checkLongBreak(AttendanceRecorded $event): void
    {
        $employee = $event->employee;
        
        // Get the most recent break-in before this break-out
        $breakIn = $employee->attendanceLogs()
            ->where('action_type', 3) // Break In
            ->where('logged_at', '<', $event->attendanceLog->logged_at)
            ->orderBy('logged_at', 'desc')
            ->first();
        
        if ($breakIn) {
            $breakMinutes = $breakIn->logged_at->diffInMinutes($event->attendanceLog->logged_at);
            
            // Trigger notification if break is longer than 60 minutes
            if ($breakMinutes > 60) {
                ProcessAttendanceNotification::dispatch(
                    $event->attendanceLog,
                    $event->employee,
                    'long_break',
                    [
                        'break_minutes' => $breakMinutes,
                        'break_in_time' => $breakIn->logged_at->format('H:i'),
                        'break_out_time' => $event->attendanceLog->logged_at->format('H:i'),
                    ]
                );
            }
        }
    }

    /**
     * Check for missing checkout from previous day
     */
    private function checkMissingCheckoutFromPreviousDay(AttendanceRecorded $event): void
    {
        $employee = $event->employee;
        $yesterday = $event->attendanceLog->logged_at->subDay()->startOfDay();
        
        // Get yesterday's last log
        $lastYesterdayLog = $employee->attendanceLogs()
            ->whereDate('logged_at', $yesterday)
            ->orderBy('logged_at', 'desc')
            ->first();
        
        // If last action was check-in, there's a missing checkout
        if ($lastYesterdayLog && $lastYesterdayLog->action_type === 1) {
            ProcessAttendanceNotification::dispatch(
                $lastYesterdayLog,
                $event->employee,
                'missing_checkout',
                [
                    'missing_date' => $yesterday->format('Y-m-d'),
                    'last_checkin_time' => $lastYesterdayLog->logged_at->format('H:i'),
                ]
            );
        }
    }

    /**
     * The job failed to process.
     */
    public function failed(AttendanceRecorded $event, \Throwable $exception): void
    {
        Log::error('HandleAttendanceRecorded listener failed', [
            'attendance_log_id' => $event->attendanceLog->id,
            'employee' => $event->employee->EMP_FullName,
            'error' => $exception->getMessage(),
        ]);
    }
}
