<?php

namespace App\Services;

use App\Models\AttendanceLog;
use App\Models\Employee;
use App\Repositories\AttendanceRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Attendance Service
 * 
 * Handles all business logic for attendance operations including
 * employee validation, attendance recording, session management,
 * and work hours calculations.
 * 
 * @package App\Services
 */
class AttendanceService
{
    public function __construct(
        private AttendanceRepository $attendanceRepository
    ) {}

    /**
     * Action type mappings
     */
    private const ACTION_TYPE_MAP = [
        'check_in' => 1,
        'check_out' => 2,
        'break_in' => 3,
        'break_out' => 4,
    ];

    private const ACTION_LABELS = [
        1 => 'Check In',
        2 => 'Check Out',
        3 => 'Break In',
        4 => 'Break Out',
    ];

    /**
     * Lookup employee by BioID and return formatted data
     */
    public function lookupEmployee(string $bioId): array
    {
        $employee = $this->attendanceRepository->findEmployeeByBioId($bioId);
        
        if (!$employee) {
            return [
                'success' => false,
                'message' => 'Employee not found',
                'employee' => null
            ];
        }

        $lastAction = $this->attendanceRepository->getLastAttendanceAction($employee);
        $currentState = $lastAction ? $lastAction->action_type : null;
        $allowedActions = $this->getAllowedActions($currentState);

        return [
            'success' => true,
            'message' => 'Employee found',
            'employee' => [
                'EMP_EmpNo' => $employee->EMP_EmpNo,
                'EMP_BioID' => $employee->EMP_BioID,
                'EMP_EmpID' => $employee->EMP_EmpID,
                'EMP_FullName' => $employee->EMP_FullName,
                'EMP_FirstName' => $employee->EMP_FirstName,
                'EMP_LastName' => $employee->EMP_LastName,
                'EMP_Department' => $employee->EMP_Department,
                'EMP_IsActive' => $employee->EMP_IsActive,
                'EMP_PhotoPath' => $employee->EMP_PhotoPath,
                'last_action' => $lastAction ? [
                    'action_type' => $lastAction->action_type,
                    'logged_at' => $lastAction->logged_at->format('M d, Y g:i A'),
                    'action_label' => self::ACTION_LABELS[$lastAction->action_type] ?? 'Unknown'
                ] : null,
                'is_checked_in' => $employee->isCheckedIn(),
                'current_state' => $currentState,
                'allowed_actions' => $allowedActions
            ]
        ];
    }

    /**
     * Record attendance action
     */
    public function recordAttendance(string $employeeCode, string $actionType): array
    {
        Log::info('Recording attendance', [
            'employee_code' => $employeeCode,
            'action_type' => $actionType,
        ]);

        // Find employee
        $employee = $this->attendanceRepository->findEmployeeByBioId($employeeCode);
        if (!$employee) {
            return [
                'success' => false,
                'message' => "No employee found with code: {$employeeCode}",
                'severity' => 'error'
            ];
        }

        // Check if employee is active
        if (!$employee->isActive()) {
            return [
                'success' => false,
                'message' => 'This user is inactive',
                'severity' => 'warn'
            ];
        }

        // Validate action type
        $actionTypeId = self::ACTION_TYPE_MAP[$actionType] ?? null;
        if (!$actionTypeId) {
            return [
                'success' => false,
                'message' => "Invalid action type: {$actionType}",
                'severity' => 'error'
            ];
        }

        // Check for duplicate attendance within 5 minutes
        if ($this->attendanceRepository->hasDuplicateAttendance($employee, $actionTypeId, 5)) {
            return [
                'success' => false,
                'message' => 'Duplicate attendance detected. Please wait before trying again.',
                'severity' => 'warn'
            ];
        }

        // Validate business rules
        $validationResult = $this->validateAttendanceAction($employee, $actionTypeId);
        if (!$validationResult['valid']) {
            return [
                'success' => false,
                'message' => $validationResult['message'],
                'severity' => 'warn'
            ];
        }

        try {
            // Create attendance log
            $log = $this->attendanceRepository->createAttendanceLog([
                'emp_no' => $employee->EMP_EmpNo,
                'bio_id' => $employee->EMP_BioID,
                'action_type' => $actionTypeId,
                'logged_at' => now(),
            ]);

            Log::info('Attendance recorded successfully', [
                'log_id' => $log->id,
                'employee' => $employee->EMP_FullName,
                'action' => $actionType,
            ]);

            $actionLabel = self::ACTION_LABELS[$actionTypeId];
            $timeFormatted = $log->logged_at->format('g:i A');

            return [
                'success' => true,
                'message' => "{$employee->EMP_FullName} - {$actionLabel} recorded successfully at {$timeFormatted}.",
                'severity' => 'success',
                'data' => [
                    'log' => $log,
                    'employee' => $employee,
                    'action_label' => $actionLabel,
                    'time_formatted' => $timeFormatted
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Failed to record attendance', [
                'employee_code' => $employeeCode,
                'action_type' => $actionType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to record attendance: ' . $e->getMessage(),
                'severity' => 'error'
            ];
        }
    }

    /**
     * Validate attendance action based on business rules
     */
    private function validateAttendanceAction(Employee $employee, int $actionTypeId): array
    {
        $lastAction = $this->attendanceRepository->getLastAttendanceAction($employee);
        $currentState = $lastAction ? $lastAction->action_type : null;

        // For now, allow all actions (no restrictions)
        // You can implement business rules here if needed
        
        return [
            'valid' => true,
            'message' => 'Action allowed'
        ];
    }

    /**
     * Get allowed actions for current state
     */
    private function getAllowedActions(?int $currentState): array
    {
        // Return all possible actions - no restrictions for now
        return ['check_in', 'check_out', 'break_in', 'break_out'];
    }

    /**
     * Generate merged sessions from attendance logs
     */
    public function generateMergedSessions(array $filters = []): array
    {
        $logs = $this->attendanceRepository->getLogsForSessions($filters);
        $sessions = [];
        $employeeLogs = $logs->groupBy('EMP_EmpNo');

        foreach ($employeeLogs as $empNo => $empLogs) {
            $employee = $empLogs->first()->employee;
            $checkIn = null;

            foreach ($empLogs as $log) {
                if ($log->action_type == 1) { // Check In
                    $checkIn = $log;
                } elseif ($log->action_type == 2 && $checkIn) { // Check Out with matching Check In
                    $duration = $checkIn->logged_at->diffInMinutes($log->logged_at);
                    $sessions[] = [
                        'employee' => $employee,
                        'check_in' => $checkIn,
                        'check_out' => $log,
                        'duration_minutes' => $duration,
                        'duration_hours' => round($duration / 60, 2),
                        'date' => $checkIn->logged_at->format('Y-m-d'),
                        'is_complete' => true
                    ];
                    $checkIn = null; // Reset for next pair
                } elseif ($log->action_type == 2 && !$checkIn) { // Unpaired Check Out
                    $sessions[] = [
                        'employee' => $employee,
                        'check_in' => null,
                        'check_out' => $log,
                        'duration_minutes' => 0,
                        'duration_hours' => 0,
                        'date' => $log->logged_at->format('Y-m-d'),
                        'is_complete' => false
                    ];
                }
            }

            // Handle unpaired check-in at the end
            if ($checkIn) {
                $sessions[] = [
                    'employee' => $employee,
                    'check_in' => $checkIn,
                    'check_out' => null,
                    'duration_minutes' => 0,
                    'duration_hours' => 0,
                    'date' => $checkIn->logged_at->format('Y-m-d'),
                    'is_complete' => false
                ];
            }
        }

        return $sessions;
    }

    /**
     * Generate hours worked summary
     */
    public function generateHoursWorkedSummary(array $filters = []): array
    {
        $sessions = $this->generateMergedSessions($filters);
        $summary = [];

        foreach ($sessions as $session) {
            $empNo = $session['employee']->EMP_EmpNo;

            if (!isset($summary[$empNo])) {
                $summary[$empNo] = [
                    'employee' => $session['employee'],
                    'total_hours' => 0,
                    'total_sessions' => 0,
                    'complete_sessions' => 0,
                    'incomplete_sessions' => 0,
                    'daily_breakdown' => []
                ];
            }

            $summary[$empNo]['total_sessions']++;

            if ($session['is_complete']) {
                $summary[$empNo]['total_hours'] += $session['duration_hours'];
                $summary[$empNo]['complete_sessions']++;

                // Daily breakdown
                $date = $session['date'];
                if (!isset($summary[$empNo]['daily_breakdown'][$date])) {
                    $summary[$empNo]['daily_breakdown'][$date] = 0;
                }
                $summary[$empNo]['daily_breakdown'][$date] += $session['duration_hours'];
            } else {
                $summary[$empNo]['incomplete_sessions']++;
            }
        }

        return array_values($summary);
    }

    /**
     * Generate consolidated hours data for export
     */
    public function generateConsolidatedHours(array $sessions): array
    {
        $employeeData = [];

        foreach ($sessions as $session) {
            $empNo = $session['employee']->EMP_EmpNo;

            if (!isset($employeeData[$empNo])) {
                $employeeData[$empNo] = [
                    'employee' => $session['employee'],
                    'total_hours' => 0,
                    'session_count' => 0,
                    'working_days' => [],
                    'sessions' => []
                ];
            }

            $empData = &$employeeData[$empNo];
            $empData['sessions'][] = $session;
            $empData['session_count']++;

            // Only count complete sessions for hours
            if ($session['is_complete'] && $session['duration_hours'] > 0) {
                $empData['total_hours'] += $session['duration_hours'];
            }

            // Track unique working days
            if ($session['date'] && !in_array($session['date'], $empData['working_days'])) {
                $empData['working_days'][] = $session['date'];
            }
        }

        // Convert to final format and calculate averages
        $result = [];
        foreach ($employeeData as $empData) {
            $workingDaysCount = count($empData['working_days']);
            $result[] = [
                'employee' => $empData['employee'],
                'total_hours' => $empData['total_hours'],
                'session_count' => $empData['session_count'],
                'working_days' => $workingDaysCount,
                'average_hours_per_day' => $workingDaysCount > 0
                    ? $empData['total_hours'] / $workingDaysCount
                    : 0,
                'sessions' => $empData['sessions']
            ];
        }

        // Sort by total hours descending
        usort($result, function($a, $b) {
            return $b['total_hours'] <=> $a['total_hours'];
        });

        return $result;
    }

    /**
     * Get attendance statistics for dashboard
     */
    public function getAttendanceStatistics(Carbon $startDate, Carbon $endDate): array
    {
        $totalLogs = $this->attendanceRepository->getAttendanceCountByDateRange($startDate, $endDate);
        $recentLogs = $this->attendanceRepository->getRecentAttendanceLogs(10);
        
        return [
            'total_logs' => $totalLogs,
            'recent_logs' => $recentLogs,
            'date_range' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d')
            ]
        ];
    }
}
