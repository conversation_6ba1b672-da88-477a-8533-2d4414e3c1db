import { ref } from 'vue'
import { useToast } from 'primevue/usetoast'
import type { Employee, ActionType } from './useEmployeeScanner'

export function useEmployeeLookup() {
  const toast = useToast()
  const isLookingUp = ref(false)

  const lookupEmployee = async (
    bioId: string,
    employeeDetails: any,
    isFromBarcodeScan: any,
    Emp_Type: any,
    employee_code: any,
    submit: () => void
  ) => {
    console.log('lookupEmployee called with bioId:', bioId, 'length:', bioId?.length)
    if (!bioId) {
      console.log('Returning early - bioId empty')
      return
    }

    // Check if we're looking up the same employee
    const isSameEmployee = employeeDetails.value && 
      (employeeDetails.value.EMP_BioID === bioId || 
       employeeDetails.value.EMP_EmpNo === bioId ||
       employeeDetails.value.EMP_FullName === bioId)

    if (isSameEmployee) {
      console.log('Same employee detected - skipping lookup, keeping current details')

      // Check if the same employee is active before allowing submission
      if (employeeDetails.value.EMP_IsActive === 0) {
        console.log('Same employee is inactive - blocking attendance logging')
        toast.add({
          severity: 'warn',
          summary: 'Employee Inactive',
          detail: 'This user is inactive',
          life: 4000
        })

        // Clear employee code to prevent any submission attempts
        employee_code.value = ''
        isFromBarcodeScan.value = false
        return
      }

      // Still handle barcode scan auto-submission for same active employee
      if (isFromBarcodeScan.value && Emp_Type.value) {
        console.log('Barcode scan detected for same active employee - setting up auto-submission')
        setTimeout(() => {
          if (employeeDetails.value && employee_code.value.trim() && Emp_Type.value) {
            console.log('Auto-submitting barcode scan for same employee')
            submit()
          }
        }, 500)
      }
      return
    }

    console.log('Starting employee lookup for:', bioId, 'isFromBarcodeScan:', isFromBarcodeScan.value)
    
    // Clear current employee details only when looking up a different employee
    if (employeeDetails.value) {
      console.log('Clearing previous employee details for new lookup')
      employeeDetails.value = null
    }
    
    isLookingUp.value = true

    try {
      const response = await fetch(route('employee.lookup', bioId))
      const data = await response.json()
      console.log('Lookup response:', data)

      if (data.success) {
        console.log('Employee found:', data.employee)
        employeeDetails.value = data.employee

        // Check if employee is active (EMP_IsActive = 1)
        if (data.employee.EMP_IsActive === 0) {
          console.log('Employee is inactive - blocking attendance logging')
          toast.add({
            severity: 'warn',
            summary: 'Employee Inactive',
            detail: 'This user is inactive',
            life: 4000
          })

          // Clear employee code to prevent any submission attempts
          employee_code.value = ''
          isFromBarcodeScan.value = false

          // Do not proceed with auto-submission for inactive employees
          return
        }

        // Show employee found toast for active employees
        toast.add({
          severity: 'info',
          summary: 'Employee Found',
          detail: `${data.employee.EMP_FullName} loaded successfully`,
          life: 5000
        })

        // Handle barcode scan auto-submission - only for active employees
        console.log('Checking auto-submission conditions:', {
          isFromBarcodeScan: isFromBarcodeScan.value,
          Emp_Type: Emp_Type.value,
          employee_code: employee_code.value.trim(),
          isActive: data.employee.EMP_IsActive
        })

        if (isFromBarcodeScan.value && Emp_Type.value) {
          console.log('Barcode scan detected - setting up auto-submission for active employee')
         
          setTimeout(() => {
            if (employeeDetails.value && employee_code.value.trim() && Emp_Type.value) {
              console.log('Auto-submitting barcode scan')
              submit()
            } else {
              console.log('Auto-submission failed - missing conditions:', {
                employeeDetails: !!employeeDetails.value,
                employee_code: employee_code.value.trim(),
                Emp_Type: Emp_Type.value
              })
            }
          }, 500) // Minimal delay just to show employee details
        } else {
          console.log('Auto-submission skipped - conditions not met:', {
            isFromBarcodeScan: isFromBarcodeScan.value,
            Emp_Type: Emp_Type.value
          })
        }
      } else {
        console.log('Employee not found')
        employeeDetails.value = null
        toast.add({
          severity: 'error',
          summary: 'Employee Not Found',
          detail: `No employee found with code: ${bioId}`,
          life: 3000
        })
      }
    } catch (error) {
      console.error('Error looking up employee:', error)
      employeeDetails.value = null
      toast.add({
        severity: 'error',
        summary: 'Lookup Error',
        detail: 'Failed to lookup employee. Please try again.',
        life: 3000
      })
    } finally {
      // Minimal delay to prevent UI flicker
      setTimeout(() => {
        isLookingUp.value = false
      }, 200)
    }
  }

  return {
    isLookingUp,
    lookupEmployee
  }
}
