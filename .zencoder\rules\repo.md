# Repository Summary

- **Framework**: <PERSON><PERSON> (PHP) with Inertia.js and Vue 3 (TypeScript tooling present)
- **Key Areas**:
  - **Backend**: `app/Http/Controllers/AttendanceController.php` manages scan lookup and attendance logging.
  - **Frontend**: `resources/js/Pages` (e.g., `Scan.vue`) for scanning UI via Inertia.
  - **Models**: `App\Models\Employee`, `App\Models\AttendanceLog`.
- **Build/Tooling**: Vite (`vite.config.ts`), ESLint, Prettier, TS config present.
- **Tests**: `tests/` plus standalone PHP test scripts in repo root.
- **Environment**: `.env` for DB and app configuration.

## Notable Behaviors
- Attendance actions use numeric IDs: 1=check_in, 2=check_out, 3=break_in, 4=break_out.
- Store action validates `employee_code` and `Emp_Type` and writes a record to `AttendanceLog`.
- Employee active status checked via `isActive()` before logging.
- Logs pages support filtering by date, employee, and action type.

## Common Tasks
- **Scan flow**: Vue component keeps input focused for continuous barcode scans, submits via Inertia to `attendance.store` (adjust route as needed).
- **Adding actions**: Update mapping in `AttendanceController::store` and hardcoded action types array in `AttendanceController::logs()`.

## Paths Reference
- `app/Http/Controllers/AttendanceController.php`
- `resources/js/Pages/Scan.vue`
- `routes/web.php`
- `config/`, `database/`, `public/`

## Tips
- Keep the scan input focused (auto-refocus on blur/click/visibility change) for reliable scanner input.
- Ensure timezone handling on `logged_at` and date filters using `log_date`.
- Validate `EMP_IsActive` for all scan submissions.