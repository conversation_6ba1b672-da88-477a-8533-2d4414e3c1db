<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Employee Photo Serving...\n";

// Test configuration loading
$config = config('filesystems.employee_photos');
echo "Allowed paths: " . implode(', ', $config['allowed_paths'] ?? []) . "\n";

// Test Employee model with absolute path
try {
    $employee = new App\Models\Employee();
    $employee->EMP_EmpID = 1; // Assuming ID 1 exists
    $employee->EMP_FirstName = 'Test';
    $employee->EMP_LastName = 'User';
    $employee->EMP_PhotoPath = 'C:/Users/<USER>/Desktop/image_50356225.jpg';
    
    echo "\nTesting absolute path photo URL generation...\n";
    echo "Photo path: " . $employee->EMP_PhotoPath . "\n";
    echo "Generated URL: " . $employee->photo_url . "\n";
    
    // Check if the file exists
    if (file_exists($employee->EMP_PhotoPath)) {
        echo "✅ Photo file exists at: " . $employee->EMP_PhotoPath . "\n";
    } else {
        echo "❌ Photo file does not exist at: " . $employee->EMP_PhotoPath . "\n";
    }
    
    // Test the route generation
    if (function_exists('route')) {
        $routeUrl = route('employee.photo', 1);
        echo "Route URL: " . $routeUrl . "\n";
    }
    
    echo "\nPhoto serving test completed!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
